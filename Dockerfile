# Use an official Python runtime as the base image
FROM python:3.10.12

# Set the working directory in the container
WORKDIR /app

# Copy requirements.txt to the working directory
COPY requirements.txt .

# Install system-level dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    build-essential \
    libsm6 \
    libxext6 \
    cmake \
    libgl1-mesa-glx \
    poppler-utils \
    libopenblas-dev \
    liblapack-dev \
    gfortran \
    && apt-get clean

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip setuptools wheel
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the project files into the container
COPY . .

# Install the profile_matching module in editable mode
RUN pip install -e app/routers/profile_matching/

# Expose the port that FastAPI will run on
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
# CMD ["bash" , "script.sh" ]
