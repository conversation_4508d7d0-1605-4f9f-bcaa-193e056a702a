from .embeddings import get_text_embedding, calculate_similarity
from .image_utils import convert_pdf_to_images, convert_image_to_base64
from .llm import extract_text_from_image, get_llm_response_with_similarity, get_llm
from .project_config import get_project_config
from .constants import REFERENCE_TEXT_FILE, DEFAULT_JD_PATH, DEFAULT_JD_TEXT_FILE
import os
import traceback
from .log_manager import logger
import datetime
from typing import TypedDict, Annotated
from langchain_core.messages import SystemMessage, HumanMessage
from .prompt import get_section_extraction_prompt, get_structured_comparison_prompt
import json
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
import tempfile
import boto3
                

# Add ensure_numbered utility

def ensure_numbered(items):
    """Ensure each item in the list is a string starting with a numbered prefix (1., 2., etc.)."""
    return [f"{i+1}. {str(item).lstrip('1234567890. ')}" for i, item in enumerate(items) if str(item).strip()]

config = get_project_config()

reference_text = None

class Category(TypedDict):
    match_percentage: Annotated[float, "Percentage of match with job requirements, from 0 to 100."]
    reason: Annotated[str, "Explanation of why this category matches or does not match the job requirements."]

class CategoryBreakDown(TypedDict):
    experience: Annotated[Category, "Work experience related to the job requirements."]
    skills: Annotated[Category, "Skills related to the job requirements."]
    education: Annotated[Category, "Education background relevant to the job requirements."]
    certifications: Annotated[Category, "Certifications relevant to the job requirements."]
    projects: Annotated[Category, "Projects relevant to the job requirements."]

class CandidateInfo(TypedDict):
    name: Annotated[str, "Full name of the candidate extracted from resume."]
    email: Annotated[str, "Email address of the candidate extracted from resume."]
    phone: Annotated[str, "Phone number of the candidate extracted from resume."]

class Analysis(TypedDict):
    candidate_info: Annotated[CandidateInfo, "Personal information of the candidate extracted from resume."]
    overall_match: Annotated[float, "Overall match percentage between 0 and 100."]
    category_breakdown: Annotated[CategoryBreakDown, "Detailed breakdown of match by category (experience, skills, etc.)."]
    key_matching_highlights: Annotated[list[str], "List of specific areas where the candidate aligns well with the job requirements."]
    key_differences: Annotated[list[str], "List of specific areas where the candidate differs from the job requirements."]
    missing_or_weak_areas: Annotated[list[str], "List of gaps or areas that are significantly underrepresented in the candidate's profile."]
    suggestions_to_improve: Annotated[list[str], "Actionable suggestions for the candidate to increase alignment with the job requirements."]

class SectionExtraction(TypedDict):
    experience: str
    skills: str
    education: str
    certifications: str
    projects: str

def extract_text_from_pdf(pdf_path):
    """Extract text from PDF by converting to images and using GPT-4 Vision."""
    logger.info(f"Extracting text from PDF using vision: {pdf_path}")
    
    # Convert PDF to images
    images = convert_pdf_to_images(pdf_path)
    if not images:
        logger.error(f"Failed to convert PDF to images: {pdf_path}")
        return f"Error: Failed to convert PDF to images"
    
    # Extract text from each image
    all_text = ""
    for i, image in enumerate(images):
        logger.info(f"Processing page {i+1}/{len(images)}")
        
        # Convert image to base64
        image_base64 = convert_image_to_base64(image)
        if not image_base64:
            logger.error(f"Error converting image {i+1} to base64 for file: {pdf_path}")
            continue
        
        logger.debug(f"Image {i+1} base64 size: {len(image_base64)}")
        
        # Extract text from image
        page_text = extract_text_from_image(image_base64)
        logger.info(f"Extracted text from page {i+1} (length: {len(page_text)})")
        all_text += f"\n\n--- PAGE {i+1} ---\n\n{page_text}"
    
    logger.info(f"Successfully extracted text from all pages (total length: {len(all_text)})")
    return all_text

def initialize_reference_resume(pdf_path):
    global reference_text
    
    logger.info(f"Initializing reference resume from: {pdf_path}")
    
    # Check if reference text file already exists
    if os.path.exists(REFERENCE_TEXT_FILE):
        logger.info(f"Loading reference text from existing file: {REFERENCE_TEXT_FILE}")
        with open(REFERENCE_TEXT_FILE, 'r', encoding='utf-8') as f:
            reference_text = f.read()
        logger.info(f"Reference text loaded successfully from file. Preview: {reference_text[:200]}...")
    else:
        # Extract text from the reference PDF
        logger.info("Reference text file not found. Extracting text from reference PDF...")
        reference_text = extract_text_from_pdf(pdf_path)
        logger.info(f"Extracted reference text from PDF. Preview: {reference_text[:200]}...")
        
        # Save the reference text to a file
        logger.info(f"Saving reference text to: {REFERENCE_TEXT_FILE}")
        with open(REFERENCE_TEXT_FILE, 'w', encoding='utf-8') as f:
            f.write(reference_text)
        logger.info("Reference text saved successfully")
    
    return reference_text

def load_reference_data():
    global reference_text
    
    logger.info("Loading reference data")
    if reference_text:
        logger.info("Using cached reference text")
        return reference_text
    
    if os.path.exists(REFERENCE_TEXT_FILE):
        logger.info(f"Loading reference text from file: {REFERENCE_TEXT_FILE}")
        with open(REFERENCE_TEXT_FILE, 'r', encoding='utf-8') as f:
            reference_text = f.read()
        return reference_text
    
    logger.info("No reference text found")
    return None

def extract_sections_with_structured_output(text):
    llm = get_llm()
    prompt = get_section_extraction_prompt(text)
    messages = [
        SystemMessage(content="You are a helpful assistant."),
        HumanMessage(content=prompt)
    ]
    structured_llm = llm.with_structured_output(SectionExtraction)
    return structured_llm.invoke(messages)

async def extract_sections_with_structured_output_async(text):
    llm = get_llm()
    prompt = get_section_extraction_prompt(text)
    messages = [
        SystemMessage(content="You are a helpful assistant."),
        HumanMessage(content=prompt)
    ]
    structured_llm = llm.with_structured_output(SectionExtraction)
    return await structured_llm.ainvoke(messages)

async def final_llm_comparison_async(reference_sections, candidate_sections, section_similarities, mode):
    from .prompt import get_structured_comparison_prompt
    from .llm import get_llm
    prompt = get_structured_comparison_prompt(reference_sections, candidate_sections, section_similarities, mode)
    messages = [
        SystemMessage(content="You are a helpful assistant."),
        HumanMessage(content=prompt)
    ]
    structured_llm = get_llm().with_structured_output(Analysis)
    return await structured_llm.ainvoke(messages)

def extract_text_from_pdf_sync(pdf_path):
    return extract_text_from_pdf(pdf_path)

async def process_resume_async(temp_path, file_name, reference_sections, mode):
    loop = asyncio.get_event_loop()
    logger.info(f"[async] [{file_name}] Extracting text from candidate PDF...")
    candidate_text = await loop.run_in_executor(None, extract_text_from_pdf_sync, temp_path)
    logger.info(f"[async] [{file_name}] Extracting sections from candidate text using LLM...")
    candidate_sections = await extract_sections_with_structured_output_async(candidate_text)
    logger.info(f"[async] [{file_name}] Candidate sections extracted: {list(candidate_sections.keys())}")
    logger.info(f"[async] [{file_name}] Candidate sections content: {json.dumps(candidate_sections, indent=2, ensure_ascii=False)}")
    section_names = ["experience", "skills", "education", "certifications", "projects"]
    section_similarities = {}
    for section in section_names:
        ref_text = reference_sections.get(section, "")
        cand_text = candidate_sections.get(section, "")
        if ref_text and cand_text:
            ref_emb = get_text_embedding(ref_text)
            cand_emb = get_text_embedding(cand_text)
            sim = calculate_similarity(ref_emb, cand_emb)
            section_similarities[section] = sim
            logger.info(f"[async] [{file_name}] Similarity for section '{section}': {sim:.4f}")
        else:
            section_similarities[section] = None
            logger.info(f"[async] [{file_name}] Similarity for section '{section}': N/A (missing text)")
    logger.info(f"[async] [{file_name}] Sending sections and similarities to LLM for final comparison...")
    result = await final_llm_comparison_async(reference_sections, candidate_sections, section_similarities, mode)
    logger.info(f"[async] [{file_name}] Final LLM response received.")
    result['file_name'] = file_name
    # --- Ensure 'category_matches' is always present for frontend compatibility ---
    category_key_map = {
        "experience": "Experience",
        "skills": "Skills",
        "education": "Education",
        "certifications": "Certifications",
        "projects": "Projects"
    }
    category_breakdown = result.get("category_breakdown", {})
    category_matches = []
    for key, label in category_key_map.items():
        cat = category_breakdown.get(key, {})
        percentage = cat.get('match_percentage', 0)
        reason = cat.get('reason', "Not provided")
        category_matches.append({
            "category": label,
            "percentage": percentage,
            "reason": reason
        })
    result["category_matches"] = category_matches
    # --- End block ---
    return result

async def process_multiple_resumes_for_jd_async(candidate_files, mode='jd'):
    import traceback
    temp_paths = []
    results = []
    try:
        for file in candidate_files:
            try:
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                    content = await file.read()
                    if not content:
                        logger.error(f"File {getattr(file, 'filename', 'unknown')} is empty!")
                    temp_file.write(content)
                    temp_paths.append((temp_file.name, getattr(file, 'filename', 'candidate.pdf')))
            except Exception as file_exc:
                logger.error(f"Exception while handling file {getattr(file, 'filename', 'unknown')}: {file_exc}")
                logger.error(traceback.format_exc())
        # Reference sections (extract once)
        if mode == "jd":
            if os.path.exists(DEFAULT_JD_TEXT_FILE):
                with open(DEFAULT_JD_TEXT_FILE, 'r', encoding='utf-8') as f:
                    reference_text = f.read()
            else:
                reference_text = initialize_default_jd(config.DEFAULT_JD_PATH)
        else:
            if os.path.exists(REFERENCE_TEXT_FILE):
                with open(REFERENCE_TEXT_FILE, 'r', encoding='utf-8') as f:
                    reference_text = f.read()
            else:
                reference_text = initialize_reference_resume(config.REFERENCE_RESUME_PATH)
        reference_sections = await extract_sections_with_structured_output_async(reference_text)
        tasks = [process_resume_async(path, name, reference_sections, mode) for path, name in temp_paths]
        task_results = await asyncio.gather(*tasks, return_exceptions=True)
        for path, _ in temp_paths:
            os.unlink(path)
        # Log and filter out exceptions
        filtered_results = []
        for r in task_results:
            if isinstance(r, Exception):
                logger.error(f"Exception in resume processing: {r}")
                logger.error(traceback.format_exc())
            else:
                filtered_results.append(r)
        filtered_results = sorted(filtered_results, key=lambda r: r.get('overall_match', 0), reverse=True)
        return filtered_results[:3]
    except Exception as e:
        logger.error(f"Exception in process_multiple_resumes_for_jd_async: {e}")
        logger.error(traceback.format_exc())
        return []

async def process_multiple_resumes_for_resume_async(candidate_files, mode='resume'):
    temp_paths = []
    for file in candidate_files:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            temp_file.write(await file.read())
            temp_paths.append((temp_file.name, getattr(file, 'filename', 'candidate.pdf')))
    # Reference sections (extract once) - use default JD
    if os.path.exists(DEFAULT_JD_TEXT_FILE):
        with open(DEFAULT_JD_TEXT_FILE, 'r', encoding='utf-8') as f:
            reference_text = f.read()
    else:
        reference_text = initialize_default_jd(config.DEFAULT_JD_PATH)
    reference_sections = await extract_sections_with_structured_output_async(reference_text)
    tasks = [process_resume_async(path, name, reference_sections, 'jd') for path, name in temp_paths]
    results = await asyncio.gather(*tasks)
    for path, _ in temp_paths:
        os.unlink(path)
    results = sorted(results, key=lambda r: r.get('overall_match', 0), reverse=True)
    return results[:3]


def compare_resumes_sectionwise(candidate_text, reference_text, reference_type="resume"):
    # 1. Extract sections from both texts using LLM
    candidate_sections = extract_sections_with_structured_output(candidate_text)
    reference_sections = extract_sections_with_structured_output(reference_text)

    # 2. Get embeddings and similarity for each section
    from .embeddings import get_text_embedding, calculate_similarity
    section_names = ["experience", "skills", "education", "certifications", "projects"]
    section_similarities = {}
    for section in section_names:
        ref_text = reference_sections.get(section, "")
        cand_text = candidate_sections.get(section, "")
        if ref_text and cand_text:
            ref_emb = get_text_embedding(ref_text)
            cand_emb = get_text_embedding(cand_text)
            sim = calculate_similarity(ref_emb, cand_emb)
            section_similarities[section] = sim
        else:
            section_similarities[section] = None

    # 3. Prepare prompt for final LLM analysis (using only sections and similarity)
    prompt = get_structured_comparison_prompt(reference_sections, candidate_sections, section_similarities, reference_type)
    llm = get_llm()
    from .working import Analysis  # or from .schemas if you move it
    messages = [
        SystemMessage(content="You are a helpful assistant."),
        HumanMessage(content=prompt)
    ]
    structured_llm = llm.with_structured_output(Analysis)
    result = structured_llm.invoke(messages)
    return result

# Remove the old full-text matching logic
def compare_resumes(candidate_text, reference_text=None, candidate_file_name=None, reference_file_name=None):
    """
    This function is deprecated and should not be used. Use match_profile or compare_resumes_sectionwise instead.
    """
    raise NotImplementedError("Full-text matching is deprecated. Use section-based matching via match_profile().")

def extract_section(text, section_name, key_name):
    """Extract a section from the text analysis and format it as a list of dictionaries."""
    try:
        # Find the section
        section_start = text.find(section_name)
        if section_start == -1:
            return []
        
        # Find the next section
        next_sections = ["Key Matching Highlights", "Key Differences", "Missing or Weak Areas", 
                         "Suggestions to Improve Match", "Category Breakdown"]
        next_sections = [s for s in next_sections if s != section_name]
        
        section_end = len(text)
        for next_section in next_sections:
            next_start = text.find(next_section, section_start + len(section_name))
            if next_start != -1 and next_start < section_end:
                section_end = next_start
        
        # Extract the section text
        section_text = text[section_start + len(section_name):section_end].strip()
        
        # Split into bullet points and clean up
        bullet_points = []
        for line in section_text.split('\n'):
            line = line.strip()
            # Skip empty lines, lines with just special characters, or section headers
            if (not line or 
                all(c in '*:-.' for c in line) or 
                line.startswith(section_name) or
                line.startswith(':')):
                continue
                
            # Remove bullet points, numbers, and other special characters
            cleaned_line = line
            # Remove leading bullet points, numbers, etc.
            import re
            cleaned_line = re.sub(r'^[\*\-\d\.\s:]+', '', cleaned_line)
            # Remove trailing special characters
            cleaned_line = cleaned_line.rstrip('*:-.')
            # Remove any remaining asterisks
            cleaned_line = cleaned_line.replace('*', '')
            
            if cleaned_line:
                bullet_points.append(cleaned_line)
        
        # Format as dictionaries
        return [{key_name: point} for point in bullet_points if point]
    except Exception as e:
        logger.error(f"Error extracting section {section_name}: {str(e)}")
        return []

def clean_section(section_items):
    """Clean up section items by removing asterisks and empty entries."""
    cleaned_items = []
    for item in section_items:
        for key, value in item.items():
            # Remove asterisks and clean up the text
            cleaned_value = value.replace('*', '').strip()
            if cleaned_value and cleaned_value != "See detailed analysis below":
                cleaned_items.append({key: cleaned_value})
    
    return cleaned_items if cleaned_items else [{"highlight": "See detailed analysis below"}]

def initialize_default_jd(pdf_path=DEFAULT_JD_PATH):
    """Extract and cache text from the default JD PDF."""
    logger.info(f"Initializing default JD from: {pdf_path}")
    # Check if default JD text file already exists
    if os.path.exists(DEFAULT_JD_TEXT_FILE):
        logger.info(f"Loading default JD text from existing file: {DEFAULT_JD_TEXT_FILE}")
        with open(DEFAULT_JD_TEXT_FILE, 'r', encoding='utf-8') as f:
            jd_text = f.read()
        logger.info(f"Default JD text loaded successfully from file. Preview: {jd_text[:200]}...")
    else:
        # Extract text from the JD PDF
        logger.info("Default JD text file not found. Extracting text from JD PDF...")
        jd_text = extract_text_from_pdf(pdf_path)
        logger.info(f"Extracted JD text from PDF. Preview: {jd_text[:200]}...")
        # Save the JD text to a file
        logger.info(f"Saving default JD text to: {DEFAULT_JD_TEXT_FILE}")
        with open(DEFAULT_JD_TEXT_FILE, 'w', encoding='utf-8') as f:
            f.write(jd_text)
        logger.info("Default JD text saved successfully")
    return jd_text

def load_default_jd_data():
    """Load cached default JD text from file."""
    logger.info("Loading default JD data")
    if os.path.exists(DEFAULT_JD_TEXT_FILE):
        logger.info(f"Loading default JD text from file: {DEFAULT_JD_TEXT_FILE}")
        with open(DEFAULT_JD_TEXT_FILE, 'r', encoding='utf-8') as f:
            jd_text = f.read()
        return jd_text
    logger.info("No default JD text found")
    return None

def match_profile(candidate_pdf_path, mode="resume", reference_resume_path=None):
    """
    Unified flow for profile matching.
    mode: "resume" (reference resume) or "jd" (job description)
    reference_resume_path: if provided and mode is 'resume', use this as the reference resume
    Returns: structured LLM response (dict)
    """
    total_start = time.time()
    
    # Log candidate information
    logger.info(f"[match_profile] ===== PROFILE MATCHING START =====")
    logger.info(f"[match_profile] Candidate PDF Path: {candidate_pdf_path}")
    logger.info(f"[match_profile] Mode: {mode}")
    logger.info(f"[match_profile] Reference Resume Path (if provided): {reference_resume_path}")
    
    # 1. Select and extract reference text
    if mode == "resume":
        if reference_resume_path is not None:
            # Use the provided reference resume path
            reference_text = extract_text_from_pdf(reference_resume_path)
            logger.info(f"[match_profile] Using provided reference resume path: {reference_resume_path}")
        else:
            # First check for custom resume S3 path, then cached reference resume text, then default reference resume PDF
            custom_resume_path_file = "artifacts/custom_reference_resume_path.txt"
            if os.path.exists(custom_resume_path_file):
                with open(custom_resume_path_file, 'r', encoding='utf-8') as f:
                    resume_s3_path = f.read().strip()
                
                # Download and extract text from S3 resume
                # resume_s3_path is already in s3://bucket/key format from router
                # Parse s3://bucket/key format
                if resume_s3_path.startswith('s3://'):
                    path_parts = resume_s3_path[5:].split('/', 1)
                    bucket_name = path_parts[0]
                    s3_key = path_parts[1] if len(path_parts) > 1 else ''
                else:
                    raise ValueError(f"Invalid S3 path format: {resume_s3_path}")
                
                # Download from S3
                s3_client = boto3.client('s3')
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                    s3_client.download_file(bucket_name, s3_key, temp_file.name)
                    resume_pdf_path = temp_file.name
                reference_text = extract_text_from_pdf(resume_pdf_path)
                
                # Clean up downloaded file
                if os.path.exists(resume_pdf_path):
                    os.unlink(resume_pdf_path)
                
                logger.info(f"[match_profile] Using custom S3 resume: {resume_s3_path}")
                logger.info(f"[match_profile] Reference Type: Custom S3 PDF Resume")
            elif os.path.exists(REFERENCE_TEXT_FILE):
                with open(REFERENCE_TEXT_FILE, 'r', encoding='utf-8') as f:
                    reference_text = f.read()
                logger.info(f"[match_profile] Using cached reference resume text: {REFERENCE_TEXT_FILE}")
                logger.info(f"[match_profile] Reference Type: Cached Reference Resume Text")
            else:
                reference_text = initialize_reference_resume(config.REFERENCE_RESUME_PATH)
                logger.info(f"[match_profile] Using default reference resume PDF: {config.REFERENCE_RESUME_PATH}")
                logger.info(f"[match_profile] Reference Type: Default PDF Resume")
    elif mode == "jd":
        # First check for custom JD text file, then cached JD text, then default JD PDF
        custom_jd_file = "artifacts/custom_reference_jd.txt"
        if os.path.exists(custom_jd_file):
            with open(custom_jd_file, 'r', encoding='utf-8') as f:
                reference_text = f.read()
            logger.info(f"[match_profile] Using custom JD text file: {custom_jd_file}")
            logger.info(f"[match_profile] Reference Type: Custom JD Text")
        elif os.path.exists(DEFAULT_JD_TEXT_FILE):
            with open(DEFAULT_JD_TEXT_FILE, 'r', encoding='utf-8') as f:
                reference_text = f.read()
            logger.info(f"[match_profile] Using cached JD text file: {DEFAULT_JD_TEXT_FILE}")
            logger.info(f"[match_profile] Reference Type: Cached JD Text")
        else:
            reference_text = initialize_default_jd(config.DEFAULT_JD_PATH)
            logger.info(f"[match_profile] Using default JD PDF: {config.DEFAULT_JD_PATH}")
            logger.info(f"[match_profile] Reference Type: Default PDF JD")
    else:
        raise ValueError("mode must be 'resume' or 'jd'")

    # 2. Extract candidate text
    logger.info("[match_profile] Extracting text from candidate PDF...")
    candidate_text = extract_text_from_pdf(candidate_pdf_path)

    # 3. Extract sections from both using LLM
    section_start = time.time()
    logger.info("[match_profile] Extracting sections from candidate text using LLM...")
    candidate_sections = extract_sections_with_structured_output(candidate_text)
    logger.info(f"[match_profile] Candidate sections extracted: {list(candidate_sections.keys())}")
    logger.info(f"[match_profile] Candidate sections content: {json.dumps(candidate_sections, indent=2, ensure_ascii=False)}")
    logger.info("[match_profile] Extracting sections from reference text using LLM...")
    reference_sections = extract_sections_with_structured_output(reference_text)
    logger.info(f"[match_profile] Reference sections extracted: {list(reference_sections.keys())}")
    logger.info(f"[match_profile] Reference sections content: {json.dumps(reference_sections, indent=2, ensure_ascii=False)}")
    section_end = time.time()
    logger.info(f"[match_profile] Section extraction (both candidate and reference) took {section_end - section_start:.2f} seconds.")

    # 4. Generate embeddings and similarity for each section
    section_names = ["experience", "skills", "education", "certifications", "projects"]
    section_similarities = {}
    for section in section_names:
        ref_text = reference_sections.get(section, "")
        cand_text = candidate_sections.get(section, "")
        if ref_text and cand_text:
            ref_emb = get_text_embedding(ref_text)
            cand_emb = get_text_embedding(cand_text)
            sim = calculate_similarity(ref_emb, cand_emb)
            section_similarities[section] = sim
            logger.info(f"[match_profile] Similarity for section '{section}': {sim:.4f}")
        else:
            section_similarities[section] = None
            logger.info(f"[match_profile] Similarity for section '{section}': N/A (missing text)")

    # 5. Send all details to LLM for the final structured response
    from .prompt import get_structured_comparison_prompt
    from .llm import get_llm
    logger.info("[match_profile] Sending sections and similarities to LLM for final comparison...")
    llm_start = time.time()
    prompt = get_structured_comparison_prompt(reference_sections, candidate_sections, section_similarities, mode)
    messages = [
        SystemMessage(content="You are a helpful assistant."),
        HumanMessage(content=prompt)
    ]
    structured_llm = get_llm().with_structured_output(Analysis)
    result = structured_llm.invoke(messages)
    llm_end = time.time()
    logger.info(f"[match_profile] Final LLM response received. LLM comparison took {llm_end - llm_start:.2f} seconds.")

    # --- Calculate overall_match if LLM didn't provide it correctly ---
    if result.get("overall_match", 0) == 0:
        category_breakdown = result.get("category_breakdown", {})
        weights = {
            "experience": 0.35,
            "skills": 0.30,
            "education": 0.15,
            "certifications": 0.10,
            "projects": 0.10
        }
        
        overall_score = 0
        for category, weight in weights.items():
            cat_data = category_breakdown.get(category, {})
            percentage = cat_data.get('match_percentage', 0)
            overall_score += percentage * weight
        
        result["overall_match"] = round(overall_score)
        logger.info(f"[match_profile] Calculated overall_match: {result['overall_match']}")

    # --- Ensure 'category_matches' is always present for frontend compatibility ---
    category_key_map = {
        "experience": "Experience",
        "skills": "Skills",
        "education": "Education",
        "certifications": "Certifications",
        "projects": "Projects"
    }
    category_breakdown = result.get("category_breakdown", {})
    category_matches = []
    for key, label in category_key_map.items():
        cat = category_breakdown.get(key, {})
        percentage = cat.get('match_percentage', 0)
        reason = cat.get('reason', "Not provided")
        category_matches.append({
            "category": label,
            "percentage": percentage,
            "reason": reason
        })
    result["category_matches"] = category_matches
    # --- End block ---

    total_end = time.time()
    logger.info(f"[match_profile] Total time taken for match_profile: {total_end - total_start:.2f} seconds.")
    logger.info(f"[match_profile] ===== PROFILE MATCHING COMPLETE =====")
    logger.info(f"[match_profile] Final overall_match: {result.get('overall_match', 0)}")
    logger.info(f"[match_profile] Returning result with {len(result.get('category_breakdown', {}))} categories")

    return result













