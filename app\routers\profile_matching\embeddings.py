from .log_manager import logger
from .project_config import get_project_config
from dotenv import load_dotenv
from functools import lru_cache
from langchain_openai import AzureOpenAIEmbeddings
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import traceback

config = get_project_config()

# Load environment variables
load_dotenv()

@lru_cache
def get_embedding_model() -> AzureOpenAIEmbeddings:
    """
    Initializes and returns an instance of AzureOpenAIEmbeddings with the specified configuration.

    Raises:
        RuntimeError: If there is an error during the initialization of AzureOpenAIEmbeddings.

    Returns:
        AzureOpenAIEmbeddings: An instance of the AzureOpenAIEmbeddings model configured with the specified parameters.
    """
    logger.debug("Initializing AzureOpenAIEmbeddings instance")
    try:
        embedding_model = AzureOpenAIEmbeddings(
            model=config.AZURE_DEPLOYMENT_EMBEDDINGS,
            openai_api_version=config.AZURE_API_VERSION_EMBEDDINGS, # type: ignore
            azure_endpoint=config.AZURE_OPENAI_ENDPOINT,
            api_key=config.AZURE_OPENAI_API_KEY,
        )
    except Exception as e:
        logger.error(f"Error initializing AzureOpenAIEmbeddings: {e}", exc_info=True)
        raise 
    return embedding_model

def get_text_embedding(text):
    """
    Get embeddings for a text using Azure OpenAI embeddings.
    
    Args:
        text (str): The text to embed
        
    Returns:
        list: The embedding vector
    """
    try:
        logger.info("Getting embedding for text")
        embedding_model = get_embedding_model()
        embedding = embedding_model.embed_query(text)
        logger.info("Successfully generated embedding")
        return embedding
    except Exception as e:
        logger.error(f"Error getting text embedding: {str(e)}")
        logger.error(traceback.format_exc())
        raise

def calculate_similarity(embedding1, embedding2):
    """
    Calculate cosine similarity between two embeddings.
    
    Args:
        embedding1 (list): First embedding vector
        embedding2 (list): Second embedding vector
        
    Returns:
        float: Cosine similarity score (0-1)
    """
    try:
        # Reshape embeddings for sklearn's cosine_similarity
        emb1 = np.array(embedding1).reshape(1, -1)
        emb2 = np.array(embedding2).reshape(1, -1)
        
        # Calculate cosine similarity
        similarity = cosine_similarity(emb1, emb2)[0][0]
        return similarity
    except Exception as e:
        logger.error(f"Error calculating similarity: {str(e)}")
        logger.error(traceback.format_exc())
        return 0.0
