# Prompt templates

from langchain_core.prompts import PromptTemplate
generate_questions_prompt_template = PromptTemplate.from_template(
    """
    Act as a {role} with 10 years of experience who conducts interviews for the role of {role}.
    The interviewee profile is as follows: 

    Primary skills: 
    {primary_skills} 

    Secondary skills: 
    {secondary_skills} 
    
    Experience: {experience} years.

    Instructions to follow:
    - Generate {no_of_questions} technical interview questions at the {difficulty_level} level, customized for a candidate with {experience} years of experience.
    - Adapt question depth and focus based on the candidate’s experience level:
        - 0 years: Emphasize core definitions, basic theory, simple examples, and learning intent.
        - <3 years: Focus on fundamental concepts, entry-level use cases, and basic real-world problem understanding.
        - 3–7 years: Include scenario-based and “how would you handle” questions that require conceptual clarity and practical insight.
        - >7 years: Emphasize deep architectural understanding, trade-offs, optimization strategies, and mentoring or decision-making scenarios.
    - For each question, strictly follow the difficulty guideline:
        - If difficulty is 'Easy': Only ask about definitions, basic usage, or simple scenarios.
        - If difficulty is 'Medium': Only ask about real-world application, common issues, and conceptual comparisons.
        - If difficulty is 'Hard': Only ask about advanced design, edge cases, optimizations, and abstract problem-solving. Do not repeat easy or medium-level questions.
    - Do not repeat the same question or concept across different difficulty levels.
    - Ensure coverage of both primary and secondary skills—at least one question per skill.
    - Avoid questions that require code writing—focus on discussion, reasoning, and explanation.
    - Return only the list of generated questions—no explanations, answers, or additional output.

    Example for the skill "Python lists" levels of easy , medium and hard:
        -  What is a list in Python?
        - How would you remove duplicates from a list in Python?
        - How would you optimize memory usage when working with very large lists in Python?
    """
)


generate_questions_jd_prompt_template = PromptTemplate.from_template(
    """
    Act as a {role} with 10 years of experience who conducts interviews for the role of {role}.
    The job description for this role is as follows:

    {job_description}

    Candidate experience: {experience} years.

    Instructions to follow:
    - Generate {no_of_questions} technical interview questions at the {difficulty_level} level, based on the job description above and customized for a candidate with {experience} years of experience.
    - Adapt question depth and focus based on the candidate’s experience level:
        - 0 years: Emphasize core definitions, basic theory, simple examples, and learning intent.
        - <3 years: Focus on fundamental concepts, entry-level use cases, and basic real-world problem understanding.
        - 3–7 years: Include scenario-based and “how would you handle” questions that require conceptual clarity and practical insight.
        - >7 years: Emphasize deep architectural understanding, trade-offs, optimization strategies, and mentoring or decision-making scenarios.
    - For each question, strictly follow the difficulty guideline:
        - If difficulty is 'Easy': Only ask about definitions, basic usage, or simple scenarios.
        - If difficulty is 'Medium': Only ask about real-world application, common issues, and conceptual comparisons.
        - If difficulty is 'Hard': Only ask about advanced design, edge cases, optimizations, and abstract problem-solving. Do not repeat easy or medium-level questions.
    - Do not repeat the same question or concept across different difficulty levels.
    - Avoid questions that require code writing—focus on discussion, reasoning, and explanation.
    - Return only the list of generated questions—no explanations, answers, or additional output.

    Example for the job description \"Python Developer\" levels of easy, medium and hard (Don't mention the difficulty level while displaying):
        - What is Python and what are its main features?
        - How would you handle package management and virtual environments in a large Python project?
        - How would you optimize the performance of a Python web application handling high traffic?
    """
)

generate_questions_hybrid_prompt_template = PromptTemplate.from_template(
    """
    Act as a {role} with 10 years of experience who conducts interviews for the role of {role}.
    The interviewee profile is as follows:

    Primary skills:
    {primary_skills}

    Secondary skills:
    {secondary_skills}

    Job Description:
    {job_description}

    Experience: {experience} years.

    Instructions to follow:
    - Generate {no_of_questions} technical interview questions at the {difficulty_level} level, with questions split between skills-based and job description-based, customized for a candidate with {experience} years of experience.
    - IMPORTANT: Job description questions should always be higher in number compared to skills questions.
    - For odd number of questions (e.g., 3): 1 skill question, 2 JD questions.
    - For even number of questions (e.g., 4): 1 skill question, 3 JD questions.
    - For even number of questions (e.g., 6): 2 skill questions, 4 JD questions.
    - Keep questions separate - do not mix skills and JD requirements in a single question.
    - IMPORTANT: Each question should be either purely skills-based OR purely job description-based. Never combine both aspects in a single question.
    - For skills questions: Focus only on the technical skills listed, without referencing job requirements.
    - For JD questions: Focus only on job-specific scenarios and requirements, without testing specific technical skills.
    - Adapt question depth and focus based on the candidate's experience level:
        - 0 years: Emphasize core definitions, basic theory, simple examples, and learning intent.
        - <3 years: Focus on fundamental concepts, entry-level use cases, and basic real-world problem understanding.
        - 3–7 years: Include scenario-based and "how would you handle" questions that require conceptual clarity and practical insight.
        - >7 years: Emphasize deep architectural understanding, trade-offs, optimization strategies, and mentoring or decision-making scenarios.
    - For each question, strictly follow the difficulty guideline:
        - If difficulty is 'Easy': Only ask about definitions, basic usage, or simple scenarios.
        - If difficulty is 'Medium': Only ask about real-world application, common issues, and conceptual comparisons.
        - If difficulty is 'Hard': Only ask about advanced design, edge cases, optimizations, and abstract problem-solving. Do not repeat easy or medium-level questions.
    - Do not repeat the same question or concept across different difficulty levels.
    - For skills-based questions: Ensure coverage of both primary and secondary skills.
    - For JD-based questions: Focus on specific requirements and responsibilities mentioned in the job description.
    - Avoid questions that require code writing—focus on discussion, reasoning, and explanation.
    - Return only the list of generated questions—no explanations, answers, or additional output.
    - Do not label or mark questions with any tags or prefixes.

    Example for the hybrid case (3 questions - skills: Python, Django; JD: web backend developer) levels of easy, medium and hard:
        - What is Python and what are its main features?
        - How would you design a high-availability backend system for our e-commerce platform?
        - What strategies would you implement to ensure data consistency in a distributed system?
    """
)


generate_questions_expected_response_format = """
{
"Questions":{"1":question_no_1,
...}}
"""

evaluate_content_prompt_template = PromptTemplate.from_template(
    """
    Act as a technical interviewer and evaluate the following Questions and answers given by the candidate with {experience} years of experience for the role of {role} and evaluate the candidate for the following primary skills and secondary skills.
    Primary skills: {primary_skills}.
    Secondary skills: {secondary_skills}.
    Questions and answers given by the candidate as follows:

    {file_content}.

    Instructions to follow while evaluating:
    - for all the above given questions generate answers based on your knowledge and check with the candidate's answer and evaluate.
    - Evaluate each question out of 5 marks.
    - At last, evaluate the average mark.
    - Rate each primary_skill out of 5 marks. If there is any question that is not available with respect to any primary skill, skip evaluating that skill.
    - Rate each secondary_skill out of 5 marks. If there is any question that is not available with respect to any secondary skill, skip evaluating that skill.
    - Generate suggestions to the candidate where he has to improve his knowledge for each primary, secondary skill and overall feedback as well based on the evaluation.
    I will provide you the expected output format. Strictly follow that format. Give only JSON as output. JSON has to be indented properly.
    """
)

evaluate_content_jd_prompt_template = PromptTemplate.from_template(
    """
    Act as a technical interviewer and evaluate the following Questions and answers given by the candidate with {experience} years of experience for the role of {role}.
    Job Description: {job_description}
    Questions and answers given by the candidate as follows:

    {file_content}.

    Instructions to follow while evaluating:
    - For all the above given questions generate answers based on your knowledge and check with the candidate's answer and evaluate.
    - Evaluate each question out of 5 marks.
    - Focus on how well the candidate's answers align with the job requirements and responsibilities.
    - Evaluate based on:
        - Understanding of job-specific scenarios
        - Problem-solving approach for role-specific challenges
        - Alignment with job requirements
        - Domain knowledge relevant to the position
    - At last, evaluate the average mark.
    - Generate suggestions for improvement based on job requirements.
    I will provide you the expected output format. Strictly follow that format. Give only JSON as output. JSON has to be indented properly.
    """
)

evaluate_content_hybrid_prompt_template = PromptTemplate.from_template(
    """
    Act as a technical interviewer and evaluate the following Questions and answers given by the candidate with {experience} years of experience for the role of {role}.
    Primary skills: {primary_skills}.
    Secondary skills: {secondary_skills}.
    Job Description: {job_description}
    Questions and answers given by the candidate as follows:

    {file_content}.

    Instructions to follow while evaluating:
    - IMPORTANT: Evaluate skills-based questions and job description-based questions separately.
    - For all questions generate answers based on your knowledge and check with the candidate's answer and evaluate.
    - Evaluate each question out of 5 marks.
    - For skills-based questions:
        - Rate each primary_skill out of 5 marks
        - Rate each secondary_skill out of 5 marks
        - Skip rating any skill that wasn't covered in the questions
    - For job description-based questions:
        - Evaluate understanding of job-specific scenarios
        - Assess problem-solving approach for role-specific challenges
        - Check alignment with job requirements
        - Evaluate domain knowledge relevant to the position
    - Calculate two separate averages:
        - Average for skills-based questions
        - Average for job description-based questions
    - Final score should weight job description performance higher than skills performance (60% JD, 40% Skills)
    - Generate separate improvement suggestions for:
        - Technical skills improvement
        - Job-specific knowledge and approach improvement
    - Provide an overall evaluation summary considering both aspects
    I will provide you the expected output format. Strictly follow that format. Give only JSON as output. JSON has to be indented properly.
    """
)

evaluate_content_expected_response_format = """
Expected response (strictly in JSON format):
{
    "marks_for_each_question": {
    "questions_related_primary_skill_1":[
        {
        "question1_asked_by_the_interviewer": "mark"
        },
        {
        "question2_asked_by_the_interviewer": "mark",
        ...}
    ],
    "questions_related_primary_skill_2":[
        {
        "question1_asked_by_the_interviewer": "mark"
        },
        {
        "question2_asked_by_the_interviewer": "mark",
        ...}
    ],
    "questions_related_secondary_skill_1":[
        {
        "question1_asked_by_the_interviewer": "mark"
        },
        {
        "question2_asked_by_the_interviewer": "mark",
        ...}
    ]},
    "ratings": {
        "primary_skills_rating": [
            {
                "skill_name": "name_of_primary_skill_1",
                "rating": "",
                "areas_to_improve_suggestion": ""
            },
            {
                "skill_name": "name_of_primary_skill_2",
                "rating": "",
                "areas_to_improve_suggestion": ""
            }
        ],
        "secondary_skills_rating": [
            {
                "skill_name": "name_of_secondary_skill_1",
                "rating": "",
                "areas_to_improve_suggestion": ""
            },
            {
                "skill_name": "name_of_secondary_skill_2",
                "rating": "",
                "areas_to_improve_suggestion": ""
            }
        ],
        "average_rating_based_on_each_question_marks": "",
        "average_rating_based_on_each_skills_rating": ""
    },
    "overall_areas_to_improve_suggestion_summary": ""
}
"""

evaluate_content_jd_expected_response_format = """
Expected response (strictly in JSON format):
{
    "marks_for_each_question": {
    "questions_related_primary_skill_1":[
        {
        "question1_asked_by_the_interviewer": "mark"
        },
        {
        "question2_asked_by_the_interviewer": "mark",
        ...}
    ],
    "questions_related_primary_skill_2":[
        {
        "question1_asked_by_the_interviewer": "mark"
        },
        {
        "question2_asked_by_the_interviewer": "mark",
        ...}
    ],
    "questions_related_secondary_skill_1":[
        {
        "question1_asked_by_the_interviewer": "mark"
        },
        {
        "question2_asked_by_the_interviewer": "mark",
        ...}
    ]},
    "ratings": {
        "primary_skills_rating": [
            {
                "skill_name": "name_of_primary_skill_1",
                "rating": "",
                "areas_to_improve_suggestion": ""
            },
            {
                "skill_name": "name_of_primary_skill_2",
                "rating": "",
                "areas_to_improve_suggestion": ""
            }
        ],
        "secondary_skills_rating": [
            {
                "skill_name": "name_of_secondary_skill_1",
                "rating": "",
                "areas_to_improve_suggestion": ""
            },
            {
                "skill_name": "name_of_secondary_skill_2",
                "rating": "",
                "areas_to_improve_suggestion": ""
            }
        ],
        "average_rating_based_on_each_question_marks": "",
        "average_rating_based_on_each_skills_rating": ""
    },
    "overall_areas_to_improve_suggestion_summary": ""
}
"""

evaluate_content_hybrid_expected_response_format = """
Expected response (strictly in JSON format):
{
    "marks_for_each_question": {
    "questions_related_primary_skill_1":[
        {
        "question1_asked_by_the_interviewer": "mark"
        },
        {
        "question2_asked_by_the_interviewer": "mark",
        ...}
    ],
    "questions_related_primary_skill_2":[
        {
        "question1_asked_by_the_interviewer": "mark"
        },
        {
        "question2_asked_by_the_interviewer": "mark",
        ...}
    ],
    "questions_related_secondary_skill_1":[
        {
        "question1_asked_by_the_interviewer": "mark"
        },
        {
        "question2_asked_by_the_interviewer": "mark",
        ...}
    ]},
    "ratings": {
        "primary_skills_rating": [
            {
                "skill_name": "name_of_primary_skill_1",
                "rating": "",
                "areas_to_improve_suggestion": ""
            },
            {
                "skill_name": "name_of_primary_skill_2",
                "rating": "",
                "areas_to_improve_suggestion": ""
            }
        ],
        "secondary_skills_rating": [
            {
                "skill_name": "name_of_secondary_skill_1",
                "rating": "",
                "areas_to_improve_suggestion": ""
            },
            {
                "skill_name": "name_of_secondary_skill_2",
                "rating": "",
                "areas_to_improve_suggestion": ""
            }
        ],
        "average_rating_based_on_each_question_marks": "",
        "average_rating_based_on_each_skills_rating": ""
    },
    "overall_areas_to_improve_suggestion_summary": ""
}
"""

JOB_DESCRIPTION_PROMPT_TEMPLATE = """
Please generate a job description for a {preferred_job_title} with the following details:

- Job Title: {preferred_job_title}
- Required Experience: {experience_in_years} years
- Employment Status: {employment_status}
- Workplace Type: {workplace_type}
- Location: {city}, {country}
- Salary Type: {salary_type}
- Primary Skills: {primary_skills}
- Secondary Skills: {secondary_skills}
- Number of Openings: {no_of_openings}

Job Summary:
Generate a job description for the role of {preferred_job_title}, including the job responsibilities, required qualifications, and a brief description of the company culture.

Make it engaging and professional to attract suitable candidates.
I want the output as html format.Use <p> tag for each paragraph and <br> tag for line breaks.Use <strong> tag for highlighting the key points. Use <ul> and <li> tags for listing the key responsibilities and qualifications.Don't add any CSS or styling to the output.Do not add any newlines, tab space or extra spaces in the output.
"""

RESUME_EXTRACTION_PROMPT = """You are a resume parsing assistant. Extract the following information from the resume image:

1. Personal Information:
   - First Name
   - Last Name
   - Email Address
   - Phone Number
   - Location (if available)
   - LinkedIn URL (if available)
   - Personal Website (if available)

2. Skills:
   - Primary Skills (technical skills, programming languages, tools)
   - Secondary Skills (soft skills, methodologies)
   - Other Skills (any additional skills)

3. Education:
   - Degree
   - Institution
   - Year
   - GPA (if available)
   - Major (if available)

4. Experience:
   - Company
   - Position
   - Duration
   - Description (list of responsibilities/achievements)

5. Additional Information:
   - Certifications
   - Languages
   - Projects (with descriptions)

Format the response as a JSON object with the following structure:
{
    "first_name": "",
    "last_name": "",
    "email": "",
    "phone_number": "",
    "location": "",
    "linkedin": "",
    "website": "",
    "primary_skills": [],
    "secondary_skills": [],
    "other_skills": [],
    "education": [
        {
            "degree": "",
            "institution": "",
            "year": "",
            "gpa": "",
            "major": ""
        }
    ],
    "experience": [
        {
            "company": "",
            "position": "",
            "duration": "",
            "description": []
        }
    ],
    "years_of_experience": 0.0,
    "certifications": [],
    "languages": [],
    "projects": [
        {
            "name": "",
            "description": ""
        }
    ],
    "full_text": ""
}

If any field cannot be found, leave it empty or use appropriate default values.
For the full_text field, include the complete text content of the resume."""

