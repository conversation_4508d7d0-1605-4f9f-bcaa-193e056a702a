import logging
from PyPDF2 import Pdf<PERSON>eader
import io
import base64

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_text_from_pdf_bytes(pdf_bytes):
    """
    Extract text from PDF bytes using PyPDF2.
    
    Args:
        pdf_bytes (bytes): PDF file as bytes
        
    Returns:
        str: Extracted text from the PDF
    """
    try:
        logger.info("Extracting text from PDF using PyPDF2")
        
        # Create a PDF file reader object
        pdf_file = io.BytesIO(pdf_bytes)
        pdf_reader = PdfReader(pdf_file)
        
        # Extract text from each page
        text = ""
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            text += page.extract_text()
            
        logger.info("Text extracted successfully from PDF")
        return text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {e}")
        return ""

def extract_text_from_base64_pdf(base64_pdf):
    """
    Extract text from a base64-encoded PDF.
    
    Args:
        base64_pdf (str): Base64-encoded PDF
        
    Returns:
        str: Extracted text from the PDF
    """
    try:
        # Decode base64 to bytes
        pdf_bytes = base64.b64decode(base64_pdf)
        
        # Extract text from PDF bytes
        return extract_text_from_pdf_bytes(pdf_bytes)
    except Exception as e:
        logger.error(f"Error decoding base64 PDF: {e}")
        return ""

def extract_text_from_pdf_file(pdf_path):
    """
    Extract text from a PDF file.
    
    Args:
        pdf_path (str): Path to the PDF file
        
    Returns:
        str: Extracted text from the PDF
    """
    try:
        logger.info(f"Extracting text from PDF file: {pdf_path}")
        
        # Read the PDF file
        with open(pdf_path, 'rb') as file:
            pdf_bytes = file.read()
            
        # Extract text from PDF bytes
        return extract_text_from_pdf_bytes(pdf_bytes)
    except Exception as e:
        logger.error(f"Error reading PDF file: {e}")
        return ""


