import os
from pymongo import MongoClient
from pymongo.errors import OperationFailure
from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware
from app.routers.routers import router as ai_router
from dotenv import load_dotenv
import uvicorn

# Load environment variables from a .env file
load_dotenv()

app = FastAPI()

# Set up CORS for FastAPI routes
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include router from your application
app.include_router(ai_router)

# Ensure default values for host and port if not set in .env
host = os.getenv("HOST")
port = int(os.getenv('PORT'))

if __name__ == "__main__":
    uvicorn.run(app, host=host, port=port)
