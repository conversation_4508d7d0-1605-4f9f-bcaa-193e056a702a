import os
import logging
from dotenv import load_dotenv
from langchain_openai import AzureChatOpenAI

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_vision_model():
    """
    Initialize and return the vision model for image processing.
    
    Returns:
        AzureChatOpenAI: Configured vision model instance
    """
    try:
        logger.info("Initializing vision model")
        
        # Use the API version from environment variable
        api_version = os.getenv("OPENAI_API_VERSION")
        
        # For vision capabilities, we need to use a model that supports it
        # Check if a vision-specific deployment is defined in environment
        vision_deployment = os.getenv("AZURE_OPENAI_VISION_DEPLOYMENT")
        
        # If no vision-specific deployment is defined, try to use a default one
        if not vision_deployment:
            logger.warning("No vision-specific deployment found in environment variables. "
                          "Using default deployment name 'gpt-4-vision'. "
                          "Set AZURE_OPENAI_VISION_DEPLOYMENT if this is incorrect.")
            vision_deployment = "gpt-4-vision"
        
        logger.info(f"Using vision deployment: {vision_deployment}, API version: {api_version}")
        
        # Initialize the vision model with Azure OpenAI
        vision_model = AzureChatOpenAI(
            azure_deployment=vision_deployment,
            api_version=api_version,
            temperature=0,
            max_tokens=4096,
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
            api_key=os.getenv("AZURE_OPENAI_API_KEY")
        )
        
        logger.info("Vision model initialized successfully")
        return vision_model
    
    except Exception as e:
        logger.error(f"Error initializing vision model: {e}")
        raise




