from .project_config import get_project_config
from pathlib import Path
import os

config = get_project_config()

# Define path to the reference resume
REFERENCE_RESUME_PATH = config.PROJECT_ROOT / "artifacts/resume/Padmanabh.pdf"
REFERENCE_TEXT_FILE = config.PROJECT_ROOT / "artifacts/reference_resume.txt"

# Define path for custom reference resume text file
CUSTOM_REFERENCE_TEXT_FILE = config.PROJECT_ROOT / "artifacts/custom_reference_resume.txt"

# POPPLER_PATH - Use environment variable if set, otherwise use local path
POPPLER_PATH = os.getenv('POPPLER_PATH', r"C:\Users\<USER>\OneDrive - Enterprise Minds\swetha.s\poppler\poppler-24.08.0\Library\bin")

DEFAULT_JD_PATH = config.PROJECT_ROOT / "artifacts/JD/JD-HORECA SALES MANAGER.pdf"
DEFAULT_JD_TEXT_FILE = config.PROJECT_ROOT / "artifacts/JD/default_jd_text.txt"
