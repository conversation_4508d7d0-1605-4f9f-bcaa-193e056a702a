from pdf2image import convert_from_path
import base64
import io
import os
import logging
from PIL import Image
from .constants import POPPLER_PATH

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add poppler to PATH
os.environ["PATH"] += os.pathsep + POPPLER_PATH

def convert_pdf_to_images(pdf_path: str) -> list:
    """
    Converts each page of a PDF to an image using pdf2image.
    
    Args:
        pdf_path (str): Path to the PDF file
        
    Returns:
        list: List of PIL Image objects
    """
    logger.info(f"Converting PDF to images: {pdf_path}")
    try:
        # Convert PDF to images using pdf2image
        images = convert_from_path(pdf_path)
        logger.info(f"Successfully converted {len(images)} pages to images")
        return images
    except Exception as e:
        logger.error(f"Error converting PDF to images: {e}")
        raise

def convert_image_to_base64(image: Image.Image) -> str:
    """
    Converts a PIL Image to a Base64 string.
    
    Args:
        image (PIL.Image): PIL Image object
        
    Returns:
        str: Base64 encoded image string
    """
    try:
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
        return img_str
    except Exception as e:
        logger.error(f"Error converting image to base64: {e}")
        raise

def combine_images(images: list) -> Image.Image:
    """
    Combines multiple images vertically.
    
    Args:
        images (list): List of PIL Image objects
        
    Returns:
        PIL.Image: Combined image
    """
    if not images:
        raise ValueError("No images to combine")
        
    # Calculate total height and max width
    total_height = sum(img.height for img in images)
    max_width = max(img.width for img in images)
    
    # Create a new image with the combined dimensions
    combined_image = Image.new('RGB', (max_width, total_height), 'white')
    
    # Paste each image into the combined image
    y_offset = 0
    for img in images:
        combined_image.paste(img, (0, y_offset))
        y_offset += img.height
        
    return combined_image

def convert_pdf_to_image(pdf_path: str) -> str:
    """
    Convert PDF to a single base64 encoded image.
    If multiple pages exist, they will be combined vertically.
    
    Args:
        pdf_path (str): Path to the PDF file
        
    Returns:
        str: Base64 encoded image string
    """
    try:
        logger.info(f"Converting PDF to image: {pdf_path}")
        
        # Convert PDF to images
        images = convert_pdf_to_images(pdf_path)
        
        # Combine images if multiple pages
        if len(images) > 1:
            logger.info("Combining multiple pages into single image")
            combined_image = combine_images(images)
        else:
            combined_image = images[0]
            
        # Convert to base64
        base64_image = convert_image_to_base64(combined_image)
        
        logger.info("PDF successfully converted to image")
        return base64_image
        
    except Exception as e:
        logger.error(f"Error converting PDF to image: {e}")
        raise 