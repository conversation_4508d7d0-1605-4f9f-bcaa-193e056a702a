def get_prompt(reference_text, candidate_text, similarity_score):
    """Generate prompt for GPT-4 after vision extraction, including similarity score."""
    return f"""
You are an expert resume evaluator helping to assess how well a candidate's resume aligns with a reference job or role description.

Use the provided reference and candidate text to:
- Compare semantic meaning (not just keyword overlap)
- Consider structure, relevance, and clarity
- Focus on matching across key dimensions like experience, technical and soft skills, education, and certifications.
- Factor in the cosine similarity score as part of your judgment. Treat it as an indicator of semantic closeness between the two texts, but use your reasoning to validate or adjust based on detailed content alignment.

Cosine Similarity Score (from embedding comparison): {similarity_score:.2f}

---

Reference (Job Description / Ideal Resume):
{reference_text}

Candidate Resume (Extracted from Image):
{candidate_text}

---

Return the following in a clear, structured format:

1. **Overall Match Percentage**: A number between 0–100 representing the overall alignment.
2. **Category Breakdown**: For each category, return a dictionary with:
    - match_percentage: a number between 0 and 100
    - reason: a brief explanation for the score

Example:
"category_breakdown": {{
    "Experience Match": {{"match_percentage": 50, "reason": "Relevant sales experience."}},
    "Skills Match": {{"match_percentage": 40, "reason": "Strong communication skills."}},
    "Education Match": {{"match_percentage": 30, "reason": "Degree in related field."}},
    "Certifications Match": {{"match_percentage": 35, "reason": "Has some relevant certifications."}},
    "Projects Match": {{"match_percentage": 40, "reason": "Led multiple sales projects."}}
}}

3. **Key Matching Highlights**: List specific areas where the candidate aligns well technically. Present each highlight as a numbered point (1., 2., etc.) with a simple sentence.

4. **Key Differences**: List qualifications, experiences, or skills that are present in the reference but missing from the candidate. Present each difference as a numbered point (1., 2., etc.) with a simple sentence.

5. **Missing or Weak Areas**: Highlight specific gaps or areas in the candidate's profile that are underrepresented or insufficiently demonstrated, even if partially present. Present each area as a numbered point (1., 2., etc.) with a simple sentence.

6. **Suggestions to Improve Match**: Provide actionable, practical steps the candidate can take to address the missing or weak areas and better align with the reference. Present each suggestion as a numbered point (1., 2., etc.) with a simple sentence.

---

Example for a candidate with a technical background applying for a sales role:

Key Differences:
1. Lacks direct sales and business development experience.
2. No demonstrated leadership or people management skills.

Missing or Weak Areas:
1. Limited exposure to client engagement and negotiation.
2. No certifications in sales or marketing.

Suggestions to Improve Match:
1. Pursue training or certification in sales and marketing.
2. Seek opportunities to lead small teams or projects to build leadership skills.
3. Gain experience in client-facing roles or internships.

---

IMPORTANT:
- Each section (Key Differences, Missing or Weak Areas, Suggestions to Improve Match) must have at least one unique, relevant, and constructive point, even if it is minor or generic.
- Do NOT repeat the same point in more than one section.
- Never return only a default message for any section. If you cannot find a specific point, provide a minor, generic, or professional development suggestion.
- For each category in the category breakdown, always return a dictionary with both match_percentage and reason.
- Provide clear, direct text without special formatting
- Present each item as a simple, clean sentence
- Use numbered points (1., 2., etc.) for all lists
"""


def get_section_extraction_prompt(resume_text):
    return f'''
You are an expert resume or JD parser.

Extract the following sections from the text below.
Return your answer as a JSON object with these keys: experience, skills, education, certifications, projects.
If a section is missing, return an empty string for that key.

Text:
---
{resume_text}
---

Example output:
{{
  "experience": "...",
  "skills": "...",
  "education": "...",
  "certifications": "...",
  "projects": "..."
}}
'''

def get_structured_comparison_prompt(reference_sections, candidate_sections, section_similarities, reference_type):
    ref_label = "Reference Resume" if reference_type == "resume" else "Job Description (JD)"
    import json
    return f'''
You are an expert in resume and job description matching.

Compare the following sections from the {ref_label} and candidate resume.
For each section (experience, skills, education, certifications, projects), provide:
- A match percentage (0-100)
- A brief reason for the score

You are also provided with cosine similarity scores (0 to 1) for each section, based on embeddings. Use these as an additional signal for your assessment.

{ref_label} Sections:
{json.dumps(reference_sections, indent=2)}

Candidate Sections:
{json.dumps(candidate_sections, indent=2)}

Cosine Similarities:
{json.dumps(section_similarities, indent=2)}

Return your answer as a JSON object with this structure:
{{
  "overall_match": 0,
  "category_breakdown": {{
    "experience": {{"match_percentage": 0, "reason": "..."}},
    "skills": {{"match_percentage": 0, "reason": "..."}},
    "education": {{"match_percentage": 0, "reason": "..."}},
    "certifications": {{"match_percentage": 0, "reason": "..."}},
    "projects": {{"match_percentage": 0, "reason": "..."}}
  }},
  "key_matching_highlights": [],
  "key_differences": [],
  "missing_or_weak_areas": [],
  "suggestions_to_improve": []

IMPORTANT: Calculate the overall_match as a weighted average of the category match percentages:
- Experience: 35% weight
- Skills: 30% weight  
- Education: 15% weight
- Certifications: 10% weight
- Projects: 10% weight

Formula: overall_match = (experience_percentage * 0.35) + (skills_percentage * 0.30) + (education_percentage * 0.15) + (certifications_percentage * 0.10) + (projects_percentage * 0.10)

Round the result to the nearest whole number.

**Key Matching Highlights**: List specific areas where the candidate aligns well technically. Present each highlight as a numbered point (1., 2., etc.) with a simple sentence.

**Key Differences**: List qualifications, experiences, or skills that are present in the reference but missing from the candidate. Present each difference as a numbered point (1., 2., etc.) with a simple sentence.

**Missing or Weak Areas**: Highlight specific gaps or areas in the candidate's profile that are underrepresented or insufficiently demonstrated, even if partially present. Present each area as a numbered point (1., 2., etc.) with a simple sentence.

**Suggestions to Improve Match**: Provide actionable, practical steps the candidate can take to address the missing or weak areas and better align with the reference. Present each suggestion as a numbered point (1., 2., etc.) with a simple sentence.

---

Example for a candidate with a technical background applying for a sales role:

Key Differences:
1. Lacks direct sales and business development experience.
2. No demonstrated leadership or people management skills.

Missing or Weak Areas:
1. Limited exposure to client engagement and negotiation.
2. No certifications in sales or marketing.

Suggestions to Improve Match:
1. Pursue training or certification in sales and marketing.
2. Seek opportunities to lead small teams or projects to build leadership skills.
3. Gain experience in client-facing roles or internships.

}}
'''