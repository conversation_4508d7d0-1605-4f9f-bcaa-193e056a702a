# AI-AUTOPILOT-INTERVIEW

This project provides an AI-driven interview platform, designed to generate and evaluate interview questions based on the role and skills specified by the user. The project is implemented using FastAPI and is structured into different modules for handling models, prompt templates, and routing.

## Project Structure

- `app/`
  - `models/`
    - `model.py`: Contains data models used throughout the application.
  - `prompt_template/`
    - `prompt_template.py`: Contains template structures for generating prompts.
  - `routers/`
    - `routers.py`: Defines API routes and endpoints.

- `.env`: Environment variables configuration file.
- `LICENSE`: License file for the project.
- `main.py`: Entry point of the application.
- `README.md`: Documentation file.
- `req.txt`: Dependencies for the project.

## Installation

1. Clone the repository:
   ```bash
   git clone -b feature-updated "https://bitbucket.org/emindsproject/ai-autopilot-interview.git"
   ```
2. Navigate to the project directory:
   ```bash
   cd AI-AUTOPILOT-INTERVIEW
   ```
3. Install the required dependencies:
   ```bash
   pip install -r req.txt
   ```
4. Run the application:
   ```bash
   python main.py
   ```

## Endpoints

### 1. Generate Questions

**Endpoint:** `/generate-questions`

**Method:** `POST`

**Description:** Generates interview questions based on the role, skills, experience, difficulty level, and the number of questions required.

**Sample Input:**
```json
{
    "role": "react developer",
    "primary_skills": ["HTML", "CSS"],
    "secondary_skills": ["Javascript"],
    "experience": 0,
    "difficulty_level": "easy",
    "no_of_questions": 2
}
```

**Response:**
```json
[
    {
        "Question": "Can you explain the box model in CSS?",
        "Answer": ""
    },
    {
        "Question": "How would you use JavaScript to manipulate the DOM?",
        "Answer": ""
    }
]
```

### 2. Evaluate Content

**Endpoint:** `/evaluate-content`

**Method:** `POST`

**Description:** Evaluates the provided answers to the generated questions based on the role and skills.

**Sample Input:**
```json
{
    "role": "react developer",
    "primary_skills": ["HTML", "CSS"],
    "secondary_skills": ["Javascript"],
    "experience": 0,
    "file_content": {
        "Questions": {
            "1": {
                "Question": "What is the main difference between HTML and CSS?",
                "Answer": "HTML (HyperText Markup Language) is used to structure the content on a web page, defining elements like headings, paragraphs, images, and links. CSS (Cascading Style Sheets) is used to control the appearance and layout of these HTML elements, such as colors, fonts, spacing, and positioning."
            },
            "2": {
                "Question": "How do you approach structuring your HTML layout in a React application?",
                "Answer": "In a React application, I structure my HTML layout by breaking down the UI into reusable components. Each component encapsulates its structure, style, and behavior, allowing for a modular and maintainable layout. I use JSX to define the structure, which allows me to write HTML-like code within JavaScript."
            }
        }
    }
}
```

**Response:**
```json
{
    "evaluation": {
        "marks_for_each_question": {
            "question1": "5",
            "question2": "5"
        },
        "ratings": {
            "average_rating": "5",
            "primary_skills_rating": [
                {
                    "name_of_primary_skill": "HTML",
                    "feedback_summary_of_primary_skill": "The candidate has a good understanding of HTML and was able to explain the difference between HTML and CSS clearly."
                },
                {
                    "name_of_primary_skill": "CSS",
                    "feedback_summary_of_primary_skill": "The candidate has a good understanding of CSS and was able to explain its usage in controlling the appearance and layout of HTML elements."
                }
            ],
            "secondary_skills_rating": [
                {
                    "name_of_secondary_skill": "Javascript",
                    "feedback_summary_of_secondary_skill": "The candidate did not have any specific question related to Javascript, but based on their answers, it seems like they have a basic understanding of it."
                }
            ],
            "overall_feedback_summary": "The candidate has a good understanding of HTML and CSS, and was able to explain their usage in a React application. They also seem to have a basic understanding of Javascript. Overall, the candidate has the potential to be a good React developer with some more experience and training."
        }
    }
}
```

## License

This project is licensed under the MIT License - see the `LICENSE` file for details.

---


---

#### Sample Input
```json
{
  
  
}
```

### Response Format

The API responds with a JSON object containing the generated job description.

#### Sample Output
```json
{
  "generated_job_desc": "<p><strong>Job Title:</strong> Data Scientist</p><p><strong>Required Experience:</strong> 3+ years</p><p><strong>Employment Status:</strong> Full time</p><p><strong>Workplace Type:</strong> Remote</p><p><strong>Location:</strong> Los Angeles, US</p><p><strong>Salary Type:</strong> Best in industry</p><p><strong>Primary Skills:</strong> Python, SQL</p><p><strong>Secondary Skills:</strong> Statistics, Generative AI</p><p><strong>Number of Openings:</strong> 5</p><p><strong>Job Summary:</strong></p><p>We are seeking a highly skilled and experienced Data Scientist to join our team. As a Data Scientist, you will be responsible for analyzing complex data sets, developing predictive models, and providing insights to support business decisions. The ideal candidate will have a strong background in Python and SQL, as well as experience with statistics and generative AI.</p><p><strong>Key Responsibilities:</strong></p><ul>  <li>Analyze large and complex data sets to identify trends and patterns</li>  <li>Develop predictive models to support business decisions</li>  <li>Collaborate with cross-functional teams to identify opportunities for data-driven insights</li>  <li>Communicate findings and insights to stakeholders in a clear and concise manner</li>  <li>Stay up-to-date with emerging trends and technologies in data science</li></ul><p><strong>Key Qualifications:</strong></p><ul>  <li>3+ years of experience in data science or a related field</li>  <li>Strong proficiency in Python and SQL</li>  <li>Experience with statistics and generative AI</li>  <li>Excellent analytical and problem-solving skills</li>  <li>Strong communication and collaboration skills</li>  <li>Bachelor's or Master's degree in Computer Science, Statistics, or a related field</li></ul><p><strong>About Our Company:</strong></p><p>We are a fast-growing company that values innovation, collaboration, and diversity. Our team is made up of talented individuals from diverse backgrounds who are passionate about using data to drive business decisions. We offer a dynamic and supportive work environment, competitive compensation, and opportunities for growth and development.</p>"
}
```

---

### Curl Command

To test the endpoint using `curl`, use the following command:

```bash
curl -X 'POST' \
  'http://127.0.0.1:8282/Api/generate-job-description' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "preferred_job_title": "Data scientist",
  "employment_status": "Full time",
  "workplace_type": "Remote",
  "country": "US",
  "city": "Los angles",
  "experience_in_years": "3+",
  "salary_type": "Best in industry",
  "primary_skills": [
    "python", "SQL"
  ],
  "secondary_skills": [
    "Statistics", "Generative AI"
  ],
  "no_of_openings": 5
}'
```

---

#### Sample Input for process_video
```json
{
    "resume_s3_bucket_path": "s3://talentwaze/interview_recording/d524a4bf-5f01-4593-99c2-86897017b981_2024-11-19T15:42:25.679Z.webm",
    "interview_s3_bucket_path": "s3://talentwaze/interview_recording/e3c4d601-49b3-4ad1-83d1-523b2ce8bd7c_2024-11-19T16:19:22.230Z.webm",
    "high_threshold": "60",
    "medium_threshold": "50",
    "low_threshold": "40",
    "mongodb_id": "4567"
}
```
1. Run the application in docker:
```bash
  ---docker build -t <image name> .

  ---docker run -d -p 8000:8000 --env-file .env -e AWS_ACCESS_KEY_ID=<replace_with_coressponding_value> -e AWS_SECRET_ACCESS_KEY=<replace_with_coressponding_value> -e AWS_DEFAULT_REGION=<replace_with_coressponding_value> -e PORT=8000 <image name>

---

### Notes

- Ensure the API server is running locally at `127.0.0.1:8282` for the above curl command to work.
- Replace the sample input values with actual data as per your requirements.

## License

This project is licensed under the MIT License - see the `LICENSE` file for details.

---

evaluation:

{
  "role": "Fullstack Web Developer",
  "primary_skills": [
    "Manual testing","selenium"
  ],
  "secondary_skills": [
    "Performance Testing"
  ],
  "experience": 0,
  "difficulty_level": "hard",
  "no_of_questions": 5,
  "question_type": "skill",
  "job_description": "<p><strong>Job Title:</strong> Software Development Engineer in Test (SDET)</p> <p><strong>Required Experience:</strong> 1+ years</p> <p><strong>Employment Status:</strong> Full-time / Contract / Hybrid</p> <p><strong>Workplace Type:</strong> Hybrid</p> <p><strong>Location:</strong> Puducherry, Pune, Hyderabad, Bangalore</p> <p><strong>Salary Type:</strong> Best in industry</p> <p><strong>Primary Skills:</strong> Java, Python, C#, JavaScript, Selenium, Cypress, Playwright, Appium</p> <p><strong>Secondary Skills:</strong> TestNG, JUnit, RESTful APIs, Postman, REST Assured, Jenkins, GitLab CI, CircleCI, Docker, Kubernetes, AWS, Azure, GCP</p> <p><strong>Number of Openings:</strong> [Specify number – e.g., 4]</p> <p><strong>Job Summary:</strong></p> <p>We are seeking a <strong>Software Development Engineer in Test (SDET)</strong> to join our Quality Engineering team. The SDET will design, develop, and maintain automated test frameworks, tools, and suites that ensure software quality and performance. This hybrid role is based in <strong>Puducherry, Pune, Hyderabad, or Bangalore</strong>, with competitive compensation and opportunities to collaborate closely with development, QA, and DevOps teams.</p> <p><strong>Key Responsibilities:</strong></p> <ul> <li>Design and implement scalable, high-quality automated test frameworks and test cases</li> <li>Collaborate with developers and product managers for comprehensive test coverage</li> <li>Build and maintain test tools and CI/CD pipelines for continuous testing and integration</li> <li>Participate in code reviews and design discussions from a testability perspective</li> <li>Identify, document, and track software defects</li> <li>Develop and execute performance, load, and stress tests</li> <li>Analyze test results; track and report quality metrics</li> <li>Contribute to continuous improvement of testing strategies and processes</li> </ul> <p><strong>Key Qualifications:</strong></p> <ul> <li>Bachelor's degree in Computer Science, Engineering, or related field</li> <li>1+ years of experience in SDET or QA automation</li> <li>Strong programming skills in Java, Python, C#, or JavaScript</li> <li>Experience with test automation tools such as Selenium, Cypress, Playwright, Appium, TestNG, or JUnit</li> <li>Hands-on experience with CI/CD tools: Jenkins, GitLab CI, or CircleCI</li> <li>Solid understanding of software testing methodologies: unit, integration, functional, regression, and performance testing</li> <li>Experience testing RESTful APIs with tools like Postman or REST Assured</li> <li>Agile/Scrum software development experience</li> </ul> <p><strong>Nice-to-Have:</strong></p> <ul> <li>Experience with cloud platforms: AWS, Azure, or GCP</li> <li>Knowledge of containerization technologies: Docker, Kubernetes</li> <li>Mobile automation testing experience</li> <li>Familiarity with BDD tools such as Cucumber or SpecFlow</li> <li>Exposure to monitoring and logging tools: ELK Stack, Datadog</li> </ul> <p>If you are passionate about quality engineering and automation, and want to play a crucial role in building robust, high-quality software solutions, we encourage you to apply and join a collaborative, innovative team.</p>"
}