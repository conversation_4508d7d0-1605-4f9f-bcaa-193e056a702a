from typing import List, Optional
from pydantic import BaseModel, EmailStr, HttpUrl

class Education(BaseModel):
    degree: str
    institution: str
    year: str
    gpa: Optional[str] = None
    major: Optional[str] = None

class Experience(BaseModel):
    company: str
    position: str
    duration: str
    description: List[str]

class Project(BaseModel):
    name: str
    description: str

class ResumeExtraction(BaseModel):
    first_name: str
    last_name: str
    email: EmailStr
    phone_number: str
    location: Optional[str] = None
    linkedin: Optional[HttpUrl] = None
    website: Optional[HttpUrl] = None
    primary_skills: List[str]
    secondary_skills: List[str]
    other_skills: List[str]
    education: List[Education]
    experience: List[Experience]
    years_of_experience: float
    certifications: List[str]
    languages: List[str]
    projects: List[Project]
    full_text: str 