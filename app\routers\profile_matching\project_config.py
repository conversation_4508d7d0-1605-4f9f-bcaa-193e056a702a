from dotenv import load_dotenv
from functools import lru_cache
from pathlib import Path
import logging
import os

load_dotenv()

class Settings:
    # Profile matching specific Azure OpenAI settings
    AZURE_OPENAI_API_KEY: str = os.getenv('AZURE_OPENAI_API_KEY_PROFILE', os.getenv('AZURE_OPENAI_API_KEY', ''))
    AZURE_OPENAI_ENDPOINT: str = os.getenv('AZURE_OPENAI_ENDPOINT_PROFILE', os.getenv('AZURE_OPENAI_ENDPOINT', ''))
    AZURE_OPENAI_API_VERSION: str = os.getenv('AZURE_OPENAI_API_VERSION', '2024-12-01-preview')
    MODEL_NAME: str = os.getenv('MODEL_NAME', 'gpt-4o')
    MODEL_DEPLOYMENT_NAME: str = os.getenv('MODEL_DEPLOYMENT_NAME', 'gpt-4o')

    AZURE_DEPLOYMENT_EMBEDDINGS: str = os.getenv('AZURE_DEPLOYMENT_EMBEDDINGS', 'text-embedding-3-small')
    AZURE_API_VERSION_EMBEDDINGS: str = os.getenv('AZURE_API_VERSION_EMBEDDINGS', '2023-05-15')

    TEMPERATURE: float = float(os.getenv('TEMPERATURE', 0.0))

    TOP_K: int = int(os.getenv('TOP_K', 5))
    PROJECT_ROOT: Path = Path(__file__).resolve().parent.parent.parent.parent

@lru_cache
def get_project_config() -> Settings:
    try:
        settings = Settings()
        logging.debug("Settings successfully loaded.")
        
        # Check if we're in Docker environment (where /app is the working directory)
        if Path("/app").exists() and Path("/app/README.md").exists():
            settings.PROJECT_ROOT = Path("/app")
        elif Path("/app").exists():
            # Docker environment but README.md might be in a different location
            # Look for README.md in the /app directory
            for item in Path("/app").iterdir():
                if item.name == "README.md":
                    settings.PROJECT_ROOT = Path("/app")
                    break
            else:
                # If README.md not found in /app, use the calculated path
                logging.info(f"Using calculated PROJECT_ROOT: {settings.PROJECT_ROOT}")
        elif "README.md" in os.listdir(settings.PROJECT_ROOT):
            # Local development - use calculated path
            pass
        else:
            # Try to find the project root by looking for README.md
            current_path = Path(__file__).resolve().parent
            while current_path != current_path.parent:
                if (current_path / "README.md").exists():
                    settings.PROJECT_ROOT = current_path
                    break
                current_path = current_path.parent
            else:
                # If still not found, use the calculated path and log info instead of warning
                logging.info(f"Using calculated PROJECT_ROOT: {settings.PROJECT_ROOT}")
        
        # Log which credentials are being used
        if os.getenv('AZURE_OPENAI_API_KEY_PROFILE'):
            logging.info("Using profile-specific Azure OpenAI credentials (AZURE_OPENAI_API_KEY_PROFILE)")
        else:
            logging.info("Using default Azure OpenAI credentials (AZURE_OPENAI_API_KEY)")
            
        if os.getenv('AZURE_OPENAI_ENDPOINT_PROFILE'):
            logging.info("Using profile-specific Azure OpenAI endpoint (AZURE_OPENAI_ENDPOINT_PROFILE)")
        else:
            logging.info("Using default Azure OpenAI endpoint (AZURE_OPENAI_ENDPOINT)")
        
        return settings
    except Exception as exe:
        logging.error(f"Error loading settings: {exe}", exc_info=True)
        raise