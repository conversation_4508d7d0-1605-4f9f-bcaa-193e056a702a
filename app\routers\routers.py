import os
import json
import boto3
import logging
from dotenv import load_dotenv
from urllib.parse import urlparse, unquote
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from app.models.model import (GenerateQuestionsRequest, EvaluateRequest,
                            FakenessEvaluationRequest, JobDescriptionInput,
                            FakenessDeductionRequest, MultipleJobDescriptionInput,
                            ResumeExtractionRequest, ResumeExtractionResponse,
                            ProfileMatchingRequest, MultipleProfileMatchingRequest,
                            SetReferenceJDTextRequest, SetReferenceResumeRequest,
                            ProfileMatchingResponse, CandidateInfo)
from langchain_openai import AzureChatOpenAI
from app.prompt_template.prompt_template import (
    generate_questions_expected_response_format,
    generate_questions_prompt_template,
    evaluate_content_expected_response_format,
    evaluate_content_prompt_template,
    evaluate_content_jd_prompt_template,
    evaluate_content_hybrid_prompt_template,
    evaluate_content_jd_expected_response_format,
    evaluate_content_hybrid_expected_response_format,
    JOB_DESCRIPTION_PROMPT_TEMPLATE,
    RESUME_EXTRACTION_PROMPT
)
import datetime
import requests
from langchain.schema import SystemMessage, HumanMessage

# Modular facemotion imports
from app.routers.fakeness_detection_module.Modular_faceMotion import FaceMotionDetection
from app.routers.fakeness_detection_module.optimized_with_multi_processing import Dlib_FaceRecognition
from app.routers.fakeness_detection_module.no_diarization_speakerRecog import SpeakerRecognition
from app.routers.fakeness_detection_module.final_decision import Final_FakenessScore
from app.routers.fakeness_detection_module.utils import *
from app.routers.resume_extract.pdf_to_image import convert_pdf_to_image
from app.routers.resume_extract.vision_model import get_vision_model

# Profile matching imports
from app.routers.profile_matching.working import match_profile, process_multiple_resumes_for_jd_async
from app.routers.profile_matching.constants import REFERENCE_RESUME_PATH, DEFAULT_JD_PATH


# Add the resume extraction route

load_dotenv()

# AWS S3 Client
s3 = boto3.client('s3')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix='/Api',
    tags=['AI Routes']
)

def initialize_llm():
    """Initialize and log LLM model creation using profile-specific credentials"""
    start_time = datetime.datetime.now()
    logger.info(f"LLM initialization START | Time: {start_time}")
    try:
        # Use profile-specific environment variables for ALL endpoints
        deployment_name = os.getenv("MODEL_DEPLOYMENT_NAME", "gpt-4.1")
        api_version = os.getenv("OPENAI_API_VERSION_PROFILE", "2023-03-15-preview")
        endpoint = os.getenv("AZURE_OPENAI_ENDPOINT_PROFILE")
        api_key = os.getenv("AZURE_OPENAI_API_KEY_PROFILE")

        logger.info(f"Using profile credentials - Deployment: {deployment_name}, API Version: {api_version}")

        llm = AzureChatOpenAI(
            azure_deployment=deployment_name,
            api_version=api_version,
            temperature=0,
            azure_endpoint=endpoint,
            api_key=api_key
        )
        end_time = datetime.datetime.now()
        logger.info(f"LLM initialization COMPLETE | Duration: {end_time - start_time}")
        return llm
    except Exception as e:
        logger.error(f"LLM initialization FAILED | Error: {str(e)} | Duration: {datetime.datetime.now() - start_time}")
        raise

def send_fakeness_deduction(roomid: str, data: FakenessDeductionRequest):
    """Send fakeness deduction results with detailed logging"""
    start_time = datetime.datetime.now()
    logger.info(f"Fakeness deduction START | RoomID: {roomid} | Time: {start_time}")
    
    url = f"https://peermeet.dev.talentwaze.eminds.ai/api/fakeness-deduction-results?roomid={roomid}"
    payload = data.dict()
    
    try:
        response = requests.post(url, json=payload, verify=False) 
        response.raise_for_status()
        end_time = datetime.datetime.now()
        logger.info(f"Fakeness deduction SUCCESS | RoomID: {roomid} | Duration: {end_time - start_time}")
        return {"message": "Data sent successfully", "response": response.json()}
    except requests.RequestException as e:
        end_time = datetime.datetime.now()
        logger.error(f"Fakeness deduction FAILED | RoomID: {roomid} | Error: {str(e)} | Duration: {end_time - start_time}")
        return {"error": f"Error sending data: {str(e)}"}

@router.post('/generate-questions')
async def generate_questions(request: GenerateQuestionsRequest):
    """Generate interview questions with time tracking and MongoDB logging"""
    start_time = datetime.datetime.now()
    mongodb_id = getattr(request, 'mongodb_id', 'N/A')
    logger.info(f"Question generation START | MongoDBID: {mongodb_id} | Time: {start_time}")
    
    try:
        llm_init_start = datetime.datetime.now()
        logger.info(f"LLM model initialization START | MongoDBID: {mongodb_id} | Time: {llm_init_start}")
        llm_model = initialize_llm()
        llm_init_end = datetime.datetime.now()
        logger.info(f"LLM model initialization COMPLETE | Duration: {llm_init_end - llm_init_start} | MongoDBID: {mongodb_id}")

        prompt_gen_start = datetime.datetime.now()
        logger.info(f"Prompt generation START | MongoDBID: {mongodb_id} | Time: {prompt_gen_start}")
        if getattr(request, 'question_type', 'skill') == 'jd':
            from app.prompt_template.prompt_template import generate_questions_jd_prompt_template
            prompt = generate_questions_jd_prompt_template.format(
                experience=request.experience,
                role=request.role,
                job_description=request.job_description or '',
                no_of_questions=request.no_of_questions,
                difficulty_level=request.difficulty_level
            )
        elif getattr(request, 'question_type', 'skill') == 'hybrid':
            from app.prompt_template.prompt_template import generate_questions_hybrid_prompt_template
            prompt = generate_questions_hybrid_prompt_template.format(
                experience=request.experience,
                role=request.role,
                primary_skills=request.primary_skills,
                secondary_skills=request.secondary_skills,
                job_description=request.job_description or '',
                no_of_questions=request.no_of_questions,
                difficulty_level=request.difficulty_level
            )
        else:
            prompt = generate_questions_prompt_template.format(
                experience=request.experience,
                role=request.role,
                primary_skills=request.primary_skills,
                secondary_skills=request.secondary_skills,
                no_of_questions=request.no_of_questions,
                difficulty_level=request.difficulty_level
            )
        complete_prompt = f"{prompt}\n{generate_questions_expected_response_format}"
        prompt_gen_end = datetime.datetime.now()
        logger.info(f"Prompt generation COMPLETE | Duration: {prompt_gen_end - prompt_gen_start} | MongoDBID: {mongodb_id}")

        llm_invoke_start = datetime.datetime.now()
        logger.info(f"LLM invocation START | MongoDBID: {mongodb_id} | Time: {llm_invoke_start}")
        evaluation = llm_model.invoke(complete_prompt)
        llm_invoke_end = datetime.datetime.now()
        logger.info(f"LLM invocation COMPLETE | Duration: {llm_invoke_end - llm_invoke_start} | MongoDBID: {mongodb_id}")

        response_string = evaluation.content
        parsing_start = datetime.datetime.now()
        logger.info(f"Response parsing START | MongoDBID: {mongodb_id} | Time: {parsing_start}")
        
        # Response parsing
        start_idx = response_string.find('{')
        end_idx = response_string.rfind('}') + 1
        if start_idx == -1 or end_idx == -1:
            logger.error(f"Response parsing FAILED | MongoDBID: {mongodb_id}")
            raise HTTPException(status_code=500, detail="Failed to parse the response")

        json_string = response_string[start_idx:end_idx]
        data = json.loads(json_string)
        
        if not isinstance(data.get('Questions'), dict):
            logger.error(f"Invalid questions format | MongoDBID: {mongodb_id}")
            raise HTTPException(status_code=500, detail="Invalid response format")

        formatted_questions = [{"Question": v, "Answer": ""} for k, v in data['Questions'].items()]
        parsing_end = datetime.datetime.now()
        logger.info(f"Response parsing COMPLETE | Duration: {parsing_end - parsing_start} | MongoDBID: {mongodb_id}")
        
        end_time = datetime.datetime.now()
        logger.info(f"Question generation COMPLETE | Total duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        return formatted_questions
        
    except Exception as e:
        end_time = datetime.datetime.now()
        logger.error(f"Question generation FAILED | Error: {str(e)} | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        raise HTTPException(status_code=500, detail=f"Failed to generate questions: {str(e)}")

@router.post('/evaluate-content')
async def evaluate_question_answers(request: EvaluateRequest):
    """Evaluate content with detailed time tracking"""
    start_time = datetime.datetime.now()
    mongodb_id = getattr(request, 'mongodb_id', 'N/A')
    logger.info(f"Content evaluation START | MongoDBID: {mongodb_id} | Time: {start_time}")
    
    try:
        llm_init_start = datetime.datetime.now()
        logger.info(f"LLM initialization START | MongoDBID: {mongodb_id} | Time: {llm_init_start}")
        llm_model = initialize_llm()
        llm_init_end = datetime.datetime.now()
        logger.info(f"LLM initialization COMPLETE | Duration: {llm_init_end - llm_init_start} | MongoDBID: {mongodb_id}")

        prompt_gen_start = datetime.datetime.now()
        logger.info(f"Prompt generation START | MongoDBID: {mongodb_id} | Time: {prompt_gen_start}")
        
        # Select appropriate template based on evaluation type
        if getattr(request, 'evaluation_type', 'skill') == 'jd':
            prompt = evaluate_content_jd_prompt_template.format(
                experience=request.experience,
                role=request.role,
                job_description=request.job_description,
                file_content=json.dumps(request.file_content)
            )
            response_format = evaluate_content_jd_expected_response_format
        elif getattr(request, 'evaluation_type', 'skill') == 'hybrid':
            prompt = evaluate_content_hybrid_prompt_template.format(
                experience=request.experience,
                role=request.role,
                primary_skills=request.primary_skills,
                secondary_skills=request.secondary_skills,
                job_description=request.job_description,
                file_content=json.dumps(request.file_content)
            )
            response_format = evaluate_content_hybrid_expected_response_format
        else:
            prompt = evaluate_content_prompt_template.format(
                experience=request.experience,
                role=request.role,
                primary_skills=request.primary_skills,
                secondary_skills=request.secondary_skills,
                file_content=json.dumps(request.file_content)
            )
            response_format = evaluate_content_expected_response_format

        complete_prompt = f"{prompt}\n{response_format}"
        prompt_gen_end = datetime.datetime.now()
        logger.info(f"Prompt generation COMPLETE | Duration: {prompt_gen_end - prompt_gen_start} | MongoDBID: {mongodb_id}")

        llm_invoke_start = datetime.datetime.now()
        logger.info(f"LLM invocation START | MongoDBID: {mongodb_id} | Time: {llm_invoke_start}")
        evaluation = llm_model.invoke(complete_prompt)
        llm_invoke_end = datetime.datetime.now()
        logger.info(f"LLM invocation COMPLETE | Duration: {llm_invoke_end - llm_invoke_start} | MongoDBID: {mongodb_id}")

        response_string = evaluation.content
        parsing_start = datetime.datetime.now()
        logger.info(f"Response parsing START | MongoDBID: {mongodb_id} | Time: {parsing_start}")

        start_idx = response_string.find('{')
        end_idx = response_string.rfind('}') + 1

        if start_idx == -1 or end_idx == -1:
            logger.error(f"Response parsing FAILED | MongoDBID: {mongodb_id}")
            raise HTTPException(status_code=500, detail="Failed to parse the response")

        json_string = response_string[start_idx:end_idx]
        data = json.loads(json_string)
        parsing_end = datetime.datetime.now()
        logger.info(f"Response parsing COMPLETE | Duration: {parsing_end - parsing_start} | MongoDBID: {mongodb_id}")

        end_time = datetime.datetime.now()
        logger.info(f"Content evaluation COMPLETE | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        return {"evaluation": data}

    except Exception as e:
        end_time = datetime.datetime.now()
        logger.error(f"Content evaluation FAILED | Error: {str(e)} | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        raise HTTPException(status_code=500, detail=f"Failed to evaluate questions and answers: {str(e)}")
    


def convert_url_format(url):
    """Convert S3 URL format with logging."""
    start_time = datetime.datetime.now()
    logger.info(f"URL conversion START | URL: {url} | Time: {start_time}")
    
    try:
        if url.startswith("s3://"):
            logger.info(f"URL is already in correct format | URL: {url}")
            return url  

        parsed_url = urlparse(url)
        
        if not parsed_url.netloc.endswith("s3.amazonaws.com"):
            logger.error(f"Invalid S3 URL format: {url}")
            raise ValueError(f"Invalid S3 URL format: {url}")

        parts = parsed_url.netloc.split(".")
        if len(parts) < 3 or parts[1] != "s3":
            logger.error(f"Unexpected S3 URL format: {url}")
            raise ValueError(f"Unexpected S3 URL format: {url}")

        bucket_name = parts[0]  
        object_key = unquote(parsed_url.path.lstrip('/'))  

        formatted_url = f"s3://{bucket_name}/{object_key}"
        end_time = datetime.datetime.now()
        logger.info(f"URL conversion COMPLETE | Converted URL: {formatted_url} | Duration: {end_time - start_time}")
        return formatted_url
    except Exception as e:
        end_time = datetime.datetime.now()
        logger.error(f"URL conversion FAILED | URL: {url} | Error: {str(e)} | Duration: {end_time - start_time}")
        raise

@router.post("/process-video/")
async def process_video(request: FakenessEvaluationRequest):
    """Process video for fakeness detection."""
    start_time = datetime.datetime.now()
    mongodb_id = request.mongodb_doc_id
    logger.info(f"Video processing START | MongoDBID: {mongodb_id} | Time: {start_time}")

    resume_s3_bucket_path = request.resume_s3_bucket_path
    interview_s3_bucket_path = request.interview_s3_bucket_path
    high_threshold = request.high_likelihood_threshold
    medium_threshold = request.medium_likelihood_threshold
    low_threshold = request.low_likelihood_threshold
    interview_video_path = None
    resume_video_path = None

    try:
        # Convert URLs to correct format before parsing
        if interview_s3_bucket_path:
            url_conversion_start = datetime.datetime.now()
            logger.info(f"Interview URL conversion START | MongoDBID: {mongodb_id} | Time: {url_conversion_start}")
            interview_s3_bucket_path = convert_url_format(interview_s3_bucket_path)
            url_conversion_end = datetime.datetime.now()
            logger.info(f"Interview URL conversion COMPLETE | Duration: {url_conversion_end - url_conversion_start} | MongoDBID: {mongodb_id}")
            
            bucket_name, s3_key = parse_s3_path(interview_s3_bucket_path)
            download_start = datetime.datetime.now()
            logger.info(f"Interview download START | Bucket: {bucket_name}, Key: {s3_key} | MongoDBID: {mongodb_id} | Time: {download_start}")
            interview_video_path = download_from_s3(bucket_name, s3_key)
            download_end = datetime.datetime.now()
            logger.info(f"Interview download COMPLETE | Duration: {download_end - download_start} | MongoDBID: {mongodb_id}")

        if resume_s3_bucket_path:
            url_conversion_start = datetime.datetime.now()
            logger.info(f"Resume URL conversion START | MongoDBID: {mongodb_id} | Time: {url_conversion_start}")
            resume_s3_bucket_path = convert_url_format(resume_s3_bucket_path)
            url_conversion_end = datetime.datetime.now()
            logger.info(f"Resume URL conversion COMPLETE | Duration: {url_conversion_end - url_conversion_start} | MongoDBID: {mongodb_id}")
            
            bucket_name, s3_key = parse_s3_path(resume_s3_bucket_path)
            download_start = datetime.datetime.now()
            logger.info(f"Resume download START | Bucket: {bucket_name}, Key: {s3_key} | MongoDBID: {mongodb_id} | Time: {download_start}")
            resume_video_path = download_from_s3(bucket_name, s3_key)
            download_end = datetime.datetime.now()
            logger.info(f"Resume download COMPLETE | Duration: {download_end - download_start} | MongoDBID: {mongodb_id}")

        # Validate that both videos are available
        if not resume_video_path:
            logger.error(f"No valid resume video provided. | MongoDBID: {mongodb_id}")
            raise HTTPException(status_code=400, detail="No valid resume video provided. Please provide a valid S3 path.")

        if not interview_video_path:
            logger.error(f"No valid interview video provided. | MongoDBID: {mongodb_id}")
            raise HTTPException(status_code=400, detail="No valid interview video provided. Please provide a valid S3 path.")

        # Face module execution
        face_module_start = datetime.datetime.now()
        logger.info(f"Face module START | MongoDBID: {mongodb_id} | Time: {face_module_start}")
        face_flag, face_result, total_analysis, two_person_frame_paths = Dlib_FaceRecognition(
            resume_video_path, interview_video_path
        ).main()
        face_module_end = datetime.datetime.now()
        logger.info(f"Face module COMPLETE | Duration: {face_module_end - face_module_start} | MongoDBID: {mongodb_id}")

        # Voice module execution
        voice_module_start = datetime.datetime.now()
        logger.info(f"Voice module START | MongoDBID: {mongodb_id} | Time: {voice_module_start}")
        voice_flag, voice_result, voice_diarization, diff_speaker_voice_file = SpeakerRecognition().main(
            resume_video_path, interview_video_path
        )
        voice_module_end = datetime.datetime.now()
        logger.info(f"Voice module COMPLETE | Duration: {voice_module_end - voice_module_start} | MongoDBID: {mongodb_id}")

        # Eye gaze and head movements module
        head_iris_module_start = datetime.datetime.now()
        logger.info(f"Head/Iris module START | MongoDBID: {mongodb_id} | Time: {head_iris_module_start}")
        output_json_path, output_video_path = process_with_face_motion(interview_video_path)
        head_iris_module_end = datetime.datetime.now()
        logger.info(f"Head/Iris module COMPLETE | Duration: {head_iris_module_end - head_iris_module_start} | MongoDBID: {mongodb_id}")

        # Final decision model
        final_decision_start = datetime.datetime.now()
        logger.info(f"Final decision START | MongoDBID: {mongodb_id} | Time: {final_decision_start}")
        probability_face_genuine = total_analysis[0]["probability_of_genuine"]
        probability_voice_genuine = voice_diarization["probability_of_genuine"]

        final_fakenessScore_obj = Final_FakenessScore(output_json_path, probability_face_genuine, probability_voice_genuine)
        likelihood = final_fakenessScore_obj.get_fakeness_score(high_threshold, medium_threshold, low_threshold)
        final_decision_end = datetime.datetime.now()
        logger.info(f"Final decision COMPLETE | Duration: {final_decision_end - final_decision_start} | MongoDBID: {mongodb_id}")

        # Store comments and likelihood after final decision model calculation
        deduction_start = datetime.datetime.now()
        logger.info(f"Fakeness deduction START | MongoDBID: {mongodb_id} | Time: {deduction_start}")
        fakeness_data = FakenessDeductionRequest(
            comments=face_result,  # Modify this based on what comments should be stored
            likelihood=likelihood
        )

        fakeness_response = send_fakeness_deduction(mongodb_id, fakeness_data)
        logger.info(f"Fakeness deduction API response: {fakeness_response} | MongoDBID: {mongodb_id}")
        deduction_end = datetime.datetime.now()
        logger.info(f"Fakeness deduction COMPLETE | Duration: {deduction_end - deduction_start} | MongoDBID: {mongodb_id}")

        end_time = datetime.datetime.now()
        logger.info(f"Video processing COMPLETE | Total duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        
        # Return the output
        return {
            "face_module_outputs": {
                'face_flag': face_flag,
                'comments': face_result,
                'total_analysis': total_analysis,
                "two_person_images": two_person_frame_paths
            },
            "voice_module_outputs": {
                'voice_flag': voice_flag,
                'voice_result': voice_result,
                'voice_analysis': voice_diarization,
                'diff_speaker_audio_file': diff_speaker_voice_file
            },
            "Head&Iris_Module_Outputs": {
                "interviewVid_output_json_path": output_json_path,
                "interviewVid_output_video_path": output_video_path
            },
            "likelihood": likelihood
        }

    except Exception as e:
        end_time = datetime.datetime.now()
        logger.error(f"Video processing FAILED | Error: {str(e)} | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        raise HTTPException(status_code=500, detail="An internal server error occurred. Please try again later.")
@router.post("/generate-job-description")
async def generate_job_description(request: JobDescriptionInput):
    """Generates a job description using the LLM model."""
    start_time = datetime.datetime.now()
    mongodb_id = getattr(request, 'mongodb_id', 'N/A')
    logger.info(f"Job description generation START | MongoDBID: {mongodb_id} | Time: {start_time}")
    try:
        llm_init_start = datetime.datetime.now()
        logger.info(f"LLM initialization START | MongoDBID: {mongodb_id} | Time: {llm_init_start}")
        llm_model = initialize_llm()
        llm_init_end = datetime.datetime.now()
        logger.info(f"LLM initialization COMPLETE | Duration: {llm_init_end - llm_init_start} | MongoDBID: {mongodb_id}")

        prompt_formatting_start = datetime.datetime.now()
        logger.info(f"Prompt formatting START | MongoDBID: {mongodb_id} | Time: {prompt_formatting_start}")
        # Generate the job description prompt
        job_description_prompt = JOB_DESCRIPTION_PROMPT_TEMPLATE.format(
            preferred_job_title=request.preferred_job_title,
            employment_status=request.employment_status,
            workplace_type=request.workplace_type,
            country=request.country,
            city=request.city,
            experience_in_years=request.experience_in_years,
            salary_type=request.salary_type,
            primary_skills=", ".join(request.primary_skills),
            secondary_skills=", ".join(request.secondary_skills),
            no_of_openings=request.no_of_openings,
        )
        logger.debug(f"Generated prompt: {job_description_prompt} | MongoDBID: {mongodb_id}")
        prompt_formatting_end = datetime.datetime.now()
        logger.info(f"Prompt formatting COMPLETE | Duration: {prompt_formatting_end - prompt_formatting_start} | MongoDBID: {mongodb_id}")

        llm_invoke_start = datetime.datetime.now()
        logger.info(f"LLM invocation START | MongoDBID: {mongodb_id} | Time: {llm_invoke_start}")
        generated_job_desc = llm_model.invoke(job_description_prompt)
        llm_invoke_end = datetime.datetime.now()
        logger.info(f"LLM invocation COMPLETE | Duration: {llm_invoke_end - llm_invoke_start} | MongoDBID: {mongodb_id}")
        
        response_string = generated_job_desc.content
        response_string = response_string.replace("\n", "").replace("\t", "")
        logger.debug(f"Response content: {response_string} | MongoDBID: {mongodb_id}")

        end_time = datetime.datetime.now()
        logger.info(f"Job description generation COMPLETE | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        # Return the generated prompt
        return {"generated_job_desc": response_string}
    except Exception as e:
        end_time = datetime.datetime.now()
        logger.error(f"Job description generation FAILED | Error: {str(e)} | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error generating job description: {e}"
        )

@router.post("/generate-multiple-job-descriptions")
async def generate_multiple_job_descriptions(request: JobDescriptionInput):
    logger.info(f"request: {request}")
    try:
        logger.info("Initializing the LLM model.")
        llm_model = initialize_llm()

        results = []
        for variation in range(2):  # Generate 2 variants
            variation_text = (
                "first variant"
                if variation == 0
                else "second variant with different wording and structure"
            )

            logger.info(f"Formatting prompt for variation {variation + 1}.")
            job_description_prompt = JOB_DESCRIPTION_PROMPT_TEMPLATE.format(
                preferred_job_title=request.preferred_job_title,
                employment_status=request.employment_status,
                workplace_type=request.workplace_type,
                country=request.country,
                city=request.city,
                experience_in_years=request.experience_in_years,
                salary_type=request.salary_type,
                primary_skills=", ".join(request.primary_skills),
                secondary_skills=", ".join(request.secondary_skills),
                no_of_openings=request.no_of_openings,
            )

            job_description_prompt += (
                f"\n\nPlease generate the {variation_text} of this job description."
            )

            logger.debug(
                f"Generated prompt (variation {variation+1}): {job_description_prompt}"
            )

            logger.info(f"Invoking the LLM model for variation {variation + 1}.")
            generated_job_desc = llm_model.invoke(job_description_prompt)
            response_string = generated_job_desc.content.replace("\n", "").replace(
                "\t", ""
            )

            logger.debug(
                f"Response content (variation {variation+1}): {response_string}"
            )

            results.append(
                {"variation": variation + 1, "generated_job_desc": response_string}
            )

        logger.info("Returning multiple job description variations.")
        return {"job_descriptions": results}

    except Exception as e:
        logger.error(f"Error generating job description: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error generating job description: {e}"
        )


@router.post("/extract-resume/", response_model=ResumeExtractionResponse)
async def extract_resume(request: ResumeExtractionRequest):
    """Extract structured information from a resume PDF."""
    start_time = datetime.datetime.now()
    mongodb_id = request.mongodb_doc_id
    logger.info(f"Resume extraction START | MongoDBID: {mongodb_id} | Time: {start_time}")
    
    try:
        # Convert S3 URL and download PDF
        resume_s3_path = convert_url_format(request.resume_s3_bucket_path)
        bucket_name, s3_key = parse_s3_path(resume_s3_path)
        pdf_path = download_from_s3(bucket_name, s3_key)
        
        # Extract text from PDF directly
        logger.info("Extracting text from PDF")
        from app.routers.resume_extract.text_extraction import extract_text_from_pdf_file
        extracted_text = extract_text_from_pdf_file(pdf_path)
        
        # Initialize LLM using shared configuration (centralized credentials)
        llm_init_start = datetime.datetime.now()
        logger.info(f"LLM initialization START | MongoDBID: {mongodb_id} | Time: {llm_init_start}")
        from app.routers.profile_matching.llm import get_llm
        llm = get_llm()
        llm_init_end = datetime.datetime.now()
        logger.info(f"LLM initialization COMPLETE | Duration: {llm_init_end - llm_init_start} | MongoDBID: {mongodb_id}")
        
        # Create messages for text-based LLM
        messages = [
            SystemMessage(content=RESUME_EXTRACTION_PROMPT),
            HumanMessage(content=f"Extract all the required information from this resume text:\n\n{extracted_text}")
        ]
        
        # Extract information using text-based LLM
        llm_invoke_start = datetime.datetime.now()
        logger.info(f"LLM invocation START | MongoDBID: {mongodb_id} | Time: {llm_invoke_start}")
        response = llm.invoke(messages)
        llm_invoke_end = datetime.datetime.now()
        logger.info(f"LLM invocation COMPLETE | Duration: {llm_invoke_end - llm_invoke_start} | MongoDBID: {mongodb_id}")
        
        # Parse the response
        try:
            extracted_data = json.loads(response.content)
        except json.JSONDecodeError:
            logger.error("Failed to parse LLM response as JSON")
            raise HTTPException(status_code=500, detail="Failed to parse resume data")
        
        # Process languages data
        languages_list = []
        languages_detailed = []
        if isinstance(extracted_data.get("languages"), list):
            for lang_item in extracted_data.get("languages", []):
                if isinstance(lang_item, dict):
                    # Store detailed language info
                    languages_detailed.append(lang_item)
                    # Also store simple string version
                    lang_name = lang_item.get("language", "")
                    proficiency = lang_item.get("proficiency", "")
                    if proficiency:
                        languages_list.append(f"{lang_name} ({proficiency})")
                    else:
                        languages_list.append(lang_name)
                elif isinstance(lang_item, str):
                    languages_list.append(lang_item)

        # Create a base response with required fields
        response_data = {
            # Basic Information
            "first_name": extracted_data.get("first_name", ""),
            "last_name": extracted_data.get("last_name", ""),
            "email": extracted_data.get("email", "<EMAIL>"),
            "phone_number": extracted_data.get("phone_number", ""),
            "location": extracted_data.get("location"),
            "linkedin": extracted_data.get("linkedin"),
            "website": extracted_data.get("website"),
            
            # Skills
            "primary_skills": extracted_data.get("primary_skills", []),
            "secondary_skills": extracted_data.get("secondary_skills", []),
            "other_skills": extracted_data.get("other_skills", []),
            
            # Education and Experience
            "education": extracted_data.get("education", []),
            "experience": extracted_data.get("experience", []),
            "years_of_experience": float(extracted_data.get("years_of_experience", 0.0)),
            
            # Additional Information
            "certifications": extracted_data.get("certifications", []),
            "languages": languages_list,
            "languages_detailed": languages_detailed,
            "projects": extracted_data.get("projects", []),
            "full_text": extracted_text if request.extract_full_text else None,
            
            # Metadata
            "mongodb_doc_id": request.mongodb_doc_id,
            "extraction_timestamp": datetime.datetime.now().isoformat()
        }

        # Add any extra fields from the extracted data
        for key, value in extracted_data.items():
            if key not in response_data and value is not None:
                logger.info(f"Adding extra field from resume: {key}")
                response_data[key] = value

        # Create the response object
        resume_data = ResumeExtractionResponse(**response_data)
        
        end_time = datetime.datetime.now()
        logger.info(f"Resume extraction COMPLETE | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        return resume_data
        
    except Exception as e:
        logger.error(f"Resume extraction FAILED | Error: {e} | Duration: {datetime.datetime.now() - start_time} | MongoDBID: {mongodb_id}")
        raise HTTPException(status_code=500, detail=str(e))

# Profile Matching Endpoints

@router.post("/match-resume-to-resume", response_model=ProfileMatchingResponse)
async def match_resume_to_resume(request: ProfileMatchingRequest):
    """Match candidate resume against reference resume using S3 paths."""
    start_time = datetime.datetime.now()
    mongodb_id = request.mongodb_doc_id
    logger.info(f"Resume-to-resume matching START | MongoDBID: {mongodb_id} | Time: {start_time}")
    logger.info(f"Candidate S3 Path: {request.candidate_resume_s3_path}")
    
    try:
        # Convert S3 URL and download PDF
        candidate_s3_path = convert_url_format(request.candidate_resume_s3_path)
        bucket_name, s3_key = parse_s3_path(candidate_s3_path)
        candidate_pdf_path = download_from_s3(bucket_name, s3_key)
        logger.info(f"Downloaded candidate PDF to: {candidate_pdf_path}")
        
        # Use the default/cached reference resume logic in working.py
        result = match_profile(candidate_pdf_path, mode="resume")
        
        # Clean up downloaded file
        if os.path.exists(candidate_pdf_path):
            os.unlink(candidate_pdf_path)
        
        # Format response - ensure correct structure
        response_data = {
            "candidate_info": result.get("candidate_info", {"name": "", "email": "", "phone": ""}),
            "overall_match": result.get("overall_match", 0.0),
            "category_breakdown": result.get("category_breakdown", {}),
            "key_matching_highlights": result.get("key_matching_highlights", []),
            "key_differences": result.get("key_differences", []),
            "missing_or_weak_areas": result.get("missing_or_weak_areas", []),
            "suggestions_to_improve": result.get("suggestions_to_improve", []),
            "mongodb_doc_id": mongodb_id,
            "processing_timestamp": datetime.datetime.now().isoformat()
        }
        
        # Ensure category_breakdown is a dictionary and doesn't contain list fields
        if isinstance(response_data["category_breakdown"], dict):
            # Remove any list fields that might have been incorrectly placed in category_breakdown
            list_fields = ["key_matching_highlights", "key_differences", "missing_or_weak_areas", "suggestions_to_improve"]
            for field in list_fields:
                if field in response_data["category_breakdown"]:
                    logger.warning(f"Removing {field} from category_breakdown as it should be a top-level field")
                    del response_data["category_breakdown"][field]
        
        end_time = datetime.datetime.now()
        logger.info(f"Resume-to-resume matching COMPLETE | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        return ProfileMatchingResponse(**response_data)
        
    except Exception as e:
        end_time = datetime.datetime.now()
        logger.error(f"Resume-to-resume matching FAILED | Error: {str(e)} | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        raise HTTPException(status_code=500, detail=f"Failed to match resume: {str(e)}")

@router.post("/match-resume-to-jd", response_model=ProfileMatchingResponse)
async def match_resume_to_jd(request: ProfileMatchingRequest):
    """Match candidate resume against job description using S3 paths."""
    start_time = datetime.datetime.now()
    mongodb_id = request.mongodb_doc_id
    logger.info(f"Resume-to-JD matching START | MongoDBID: {mongodb_id} | Time: {start_time}")
    logger.info(f"Candidate S3 Path: {request.candidate_resume_s3_path}")
    
    try:
        # Convert S3 URL and download PDF
        candidate_s3_path = convert_url_format(request.candidate_resume_s3_path)
        bucket_name, s3_key = parse_s3_path(candidate_s3_path)
        candidate_pdf_path = download_from_s3(bucket_name, s3_key)
        logger.info(f"Downloaded candidate PDF to: {candidate_pdf_path}")
        
        # Use the default/cached JD logic in working.py
        result = match_profile(candidate_pdf_path, mode="jd")
        
        # Clean up downloaded file
        if os.path.exists(candidate_pdf_path):
            os.unlink(candidate_pdf_path)
        
        # Format response - ensure correct structure
        response_data = {
            "candidate_info": result.get("candidate_info", {"name": "", "email": "", "phone": ""}),
            "overall_match": result.get("overall_match", 0.0),
            "category_breakdown": result.get("category_breakdown", {}),
            "key_matching_highlights": result.get("key_matching_highlights", []),
            "key_differences": result.get("key_differences", []),
            "missing_or_weak_areas": result.get("missing_or_weak_areas", []),
            "suggestions_to_improve": result.get("suggestions_to_improve", []),
            "mongodb_doc_id": mongodb_id,
            "processing_timestamp": datetime.datetime.now().isoformat()
        }
        
        # Ensure category_breakdown is a dictionary and doesn't contain list fields
        if isinstance(response_data["category_breakdown"], dict):
            # Remove any list fields that might have been incorrectly placed in category_breakdown
            list_fields = ["key_matching_highlights", "key_differences", "missing_or_weak_areas", "suggestions_to_improve"]
            for field in list_fields:
                if field in response_data["category_breakdown"]:
                    logger.warning(f"Removing {field} from category_breakdown as it should be a top-level field")
                    del response_data["category_breakdown"][field]
        
        end_time = datetime.datetime.now()
        logger.info(f"Resume-to-JD matching COMPLETE | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        return ProfileMatchingResponse(**response_data)
        
    except Exception as e:
        end_time = datetime.datetime.now()
        logger.error(f"Resume-to-JD matching FAILED | Error: {str(e)} | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        raise HTTPException(status_code=500, detail=f"Failed to match resume to JD: {str(e)}")

@router.post("/multiple-match-resumes-to-jd")
async def multiple_match_resumes_to_jd(request: MultipleProfileMatchingRequest):
    """Match multiple candidate resumes against job description using S3 paths."""
    start_time = datetime.datetime.now()
    mongodb_id = request.mongodb_doc_id
    logger.info(f"Multiple resume-to-JD matching START | MongoDBID: {mongodb_id} | Time: {start_time}")
    
    try:
        results = []
        temp_files = []
        
        for i, s3_path in enumerate(request.candidate_resume_s3_paths):
            try:
                # Convert S3 URL and download PDF
                candidate_s3_path = convert_url_format(s3_path)
                bucket_name, s3_key = parse_s3_path(candidate_s3_path)
                candidate_pdf_path = download_from_s3(bucket_name, s3_key)
                temp_files.append(candidate_pdf_path)
                
                # Process the resume
                result = match_profile(candidate_pdf_path, mode="jd")
                
                # Add file info to result
                result["file_index"] = i
                result["s3_path"] = s3_path
                results.append(result)
                
            except Exception as e:
                logger.error(f"Failed to process resume {i}: {str(e)}")
                results.append({
                    "file_index": i,
                    "s3_path": s3_path,
                    "error": str(e),
                    "overall_match": 0.0
                })
        
        # Clean up downloaded files
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
        
        end_time = datetime.datetime.now()
        logger.info(f"Multiple resume-to-JD matching COMPLETE | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        return {"results": results, "mongodb_doc_id": mongodb_id}
        
    except Exception as e:
        end_time = datetime.datetime.now()
        logger.error(f"Multiple resume-to-JD matching FAILED | Error: {str(e)} | Duration: {end_time - start_time} | MongoDBID: {mongodb_id}")
        raise HTTPException(status_code=500, detail=f"Failed to process multiple resumes: {str(e)}")

@router.get("/default-reference-resume")
def get_default_reference_resume():
    """Get information about the default reference resume."""
    try:
        if os.path.exists(REFERENCE_RESUME_PATH):
            from app.routers.profile_matching.working import extract_text_from_pdf
            text = extract_text_from_pdf(REFERENCE_RESUME_PATH)
            return {
                "preview": text[:200] + "..." if len(text) > 200 else text,
                "length": len(text),
                "file_path": str(REFERENCE_RESUME_PATH)
            }
        else:
            return {"error": "Default reference resume not found."}
    except Exception as e:
        logger.error(f"Error getting default reference resume: {str(e)}")
        return {"error": str(e)}

@router.get("/default-jd")
def get_default_jd():
    """Get information about the default job description."""
    try:
        if os.path.exists(DEFAULT_JD_PATH):
            from app.routers.profile_matching.working import extract_text_from_pdf
            text = extract_text_from_pdf(DEFAULT_JD_PATH)
            return {
                "preview": text[:200] + "..." if len(text) > 200 else text,
                "length": len(text),
                "file_path": str(DEFAULT_JD_PATH)
            }
        else:
            return {"error": "Default JD not found."}
    except Exception as e:
        logger.error(f"Error getting default JD: {str(e)}")
        return {"error": str(e)}

@router.post("/set-reference-jd-text")
def set_reference_jd_text(request: SetReferenceJDTextRequest):
    """Set a custom job description text as the reference JD."""
    try:
        jd_text = request.job_description_text
        if not jd_text or not jd_text.strip():
            raise HTTPException(status_code=400, detail="Job description text cannot be empty")
        
        # Store the JD text in a file for future use
        jd_text_file = "artifacts/custom_reference_jd.txt"
        os.makedirs(os.path.dirname(jd_text_file), exist_ok=True)
        
        with open(jd_text_file, 'w', encoding='utf-8') as f:
            f.write(jd_text)
        
        logger.info(f"Reference JD text set successfully. Length: {len(jd_text)}")
        return {
            "message": "Reference JD text set successfully",
            "preview": jd_text[:200] + "..." if len(jd_text) > 200 else jd_text,
            "length": len(jd_text),
            "file_path": jd_text_file
        }
    except Exception as e:
        logger.error(f"Error setting reference JD text: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error setting reference JD text: {str(e)}")

@router.get("/get-reference-jd")
def get_reference_jd():
    """Get the current reference job description (PDF or custom text)."""
    try:
        # First check if there's a custom JD text file
        custom_jd_file = "artifacts/custom_reference_jd.txt"
        if os.path.exists(custom_jd_file):
            with open(custom_jd_file, 'r', encoding='utf-8') as f:
                text = f.read()
            return {
                "type": "custom_text",
                "preview": text[:200] + "..." if len(text) > 200 else text,
                "length": len(text),
                "file_path": custom_jd_file
            }
        
        # Fall back to default PDF JD
        if os.path.exists(DEFAULT_JD_PATH):
            from app.routers.profile_matching.working import extract_text_from_pdf
            text = extract_text_from_pdf(DEFAULT_JD_PATH)
            return {
                "type": "default_pdf",
                "preview": text[:200] + "..." if len(text) > 200 else text,
                "length": len(text),
                "file_path": str(DEFAULT_JD_PATH)
            }
        else:
            return {"error": "No reference JD found (neither custom text nor default PDF)."}
    except Exception as e:
        logger.error(f"Error getting reference JD: {str(e)}")
        return {"error": str(e)}

@router.post("/set-reference-resume")
def set_reference_resume(request: SetReferenceResumeRequest):
    """Set a custom resume PDF from S3 as the reference resume."""
    try:
        resume_s3_path = request.resume_s3_bucket_path
        if not resume_s3_path or not resume_s3_path.strip():
            raise HTTPException(status_code=400, detail="Resume S3 path cannot be empty")
        
        # Convert S3 URL format
        converted_s3_path = convert_url_format(resume_s3_path)
        
        # Convert S3 URL and download PDF for preview
        bucket_name, s3_key = parse_s3_path(converted_s3_path)
        resume_pdf_path = download_from_s3(bucket_name, s3_key)
        
        # Store the converted S3 path for future use
        resume_path_file = "artifacts/custom_reference_resume_path.txt"
        os.makedirs(os.path.dirname(resume_path_file), exist_ok=True)
        
        with open(resume_path_file, 'w', encoding='utf-8') as f:
            f.write(converted_s3_path)
        
        # Extract text for preview
        from app.routers.profile_matching.working import extract_text_from_pdf
        text = extract_text_from_pdf(resume_pdf_path)
        
        # Clean up downloaded file
        if os.path.exists(resume_pdf_path):
            os.unlink(resume_pdf_path)
        
        logger.info(f"Reference resume set successfully. S3 Path: {converted_s3_path}")
        return {
            "message": "Reference resume set successfully",
            "preview": text[:200] + "..." if len(text) > 200 else text,
            "length": len(text),
            "s3_path": converted_s3_path
        }
    except Exception as e:
        logger.error(f"Error setting reference resume: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error setting reference resume: {str(e)}")

@router.get("/get-reference-resume")
def get_reference_resume():
    """Get the current reference resume (custom S3 PDF or default PDF)."""
    try:
        # First check if there's a custom resume S3 path file
        custom_resume_path_file = "artifacts/custom_reference_resume_path.txt"
        if os.path.exists(custom_resume_path_file):
            with open(custom_resume_path_file, 'r', encoding='utf-8') as f:
                resume_s3_path = f.read().strip()
            
            # Download and extract text for preview (URL is already converted)
            bucket_name, s3_key = parse_s3_path(resume_s3_path)
            resume_pdf_path = download_from_s3(bucket_name, s3_key)
            
            from app.routers.profile_matching.working import extract_text_from_pdf
            text = extract_text_from_pdf(resume_pdf_path)
            
            # Clean up downloaded file
            if os.path.exists(resume_pdf_path):
                os.unlink(resume_pdf_path)
            
            return {
                "type": "custom_s3_pdf",
                "preview": text[:200] + "..." if len(text) > 200 else text,
                "length": len(text),
                "s3_path": resume_s3_path
            }
        
        # Fall back to default PDF resume
        if os.path.exists(REFERENCE_RESUME_PATH):
            from app.routers.profile_matching.working import extract_text_from_pdf
            text = extract_text_from_pdf(REFERENCE_RESUME_PATH)
            return {
                "type": "default_pdf",
                "preview": text[:200] + "..." if len(text) > 200 else text,
                "length": len(text),
                "file_path": str(REFERENCE_RESUME_PATH)
            }
        else:
            return {"error": "No reference resume found (neither custom S3 PDF nor default PDF)."}
    except Exception as e:
        logger.error(f"Error getting reference resume: {str(e)}")
        return {"error": str(e)}
