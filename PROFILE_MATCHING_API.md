# Profile Matching API Documentation

This document describes the new profile matching endpoints that have been integrated into the AI-Autopilot Interview Service.

## Overview

The profile matching functionality allows you to:
- Compare candidate resumes against reference resumes
- Match candidate resumes against job descriptions
- Process multiple resumes in batch
- Get detailed analysis with match percentages and recommendations

## Base URL

All endpoints are prefixed with `/Api` and are part of the existing FastAPI application.

## Endpoints

### 1. Match Resume to Resume

**Endpoint:** `POST /Api/match-resume-to-resume`

**Description:** Compares a candidate resume against a reference resume to determine how well they match.

**Request Body:**
```json
{
    "candidate_resume_s3_path": "https://talentwaze.s3.amazonaws.com/candidates-resume/Ramesh%20N.pdf",
    "mongodb_doc_id": "unique_identifier_123"
}
```

**Response:**
```json
{
    "overall_match": 75.5,
    "category_breakdown": {
        "experience": {
            "match_percentage": 80.0,
            "reason": "Strong relevant experience in similar roles"
        },
        "skills": {
            "match_percentage": 70.0,
            "reason": "Good technical skills alignment"
        },
        "education": {
            "match_percentage": 65.0,
            "reason": "Similar educational background"
        },
        "certifications": {
            "match_percentage": 60.0,
            "reason": "Some relevant certifications"
        },
        "projects": {
            "match_percentage": 75.0,
            "reason": "Good project experience"
        }
    },
    "key_matching_highlights": [
        "1. Strong experience in React development",
        "2. Excellent communication skills",
        "3. Relevant certifications in web development"
    ],
    "key_differences": [
        "1. Different industry experience",
        "2. Varying leadership experience levels"
    ],
    "missing_or_weak_areas": [
        "1. Limited experience with cloud platforms",
        "2. No demonstrated team leadership"
    ],
    "suggestions_to_improve": [
        "1. Pursue AWS or Azure certifications",
        "2. Seek opportunities to lead small teams"
    ],
    "mongodb_doc_id": "unique_identifier_123",
    "processing_timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 2. Match Resume to Job Description

**Endpoint:** `POST /Api/match-resume-to-jd`

**Description:** Compares a candidate resume against a job description to determine suitability for the role.

**Request Body:**
```json
{
    "candidate_resume_s3_path": "https://talentwaze.s3.amazonaws.com/candidates-resume/Ramesh%20N.pdf",
    "mongodb_doc_id": "unique_identifier_456"
}
```

**Response:** Same structure as resume-to-resume matching.

### 3. Multiple Resume Matching

**Endpoint:** `POST /Api/multiple-match-resumes-to-jd`

**Description:** Processes multiple candidate resumes against a job description in batch.

**Request Body:**
```json
{
    "candidate_resume_s3_paths": [
        "https://talentwaze.s3.amazonaws.com/candidates-resume/Ramesh%20N.pdf",
        "https://talentwaze.s3.amazonaws.com/candidates-resume/John%20Doe.pdf",
        "https://talentwaze.s3.amazonaws.com/candidates-resume/Jane%20Smith.pdf"
    ],
    "mongodb_doc_id": "batch_identifier_789"
}
```

**Response:**
```json
{
    "results": [
        {
            "file_index": 0,
            "s3_path": "https://talentwaze.s3.amazonaws.com/candidates-resume/Ramesh%20N.pdf",
            "overall_match": 85.0,
            "category_breakdown": { ... },
            "key_matching_highlights": [ ... ],
            "key_differences": [ ... ],
            "missing_or_weak_areas": [ ... ],
            "suggestions_to_improve": [ ... ]
        },
        {
            "file_index": 1,
            "s3_path": "https://talentwaze.s3.amazonaws.com/candidates-resume/John%20Doe.pdf",
            "overall_match": 65.0,
            "category_breakdown": { ... },
            "key_matching_highlights": [ ... ],
            "key_differences": [ ... ],
            "missing_or_weak_areas": [ ... ],
            "suggestions_to_improve": [ ... ]
        }
    ],
    "mongodb_doc_id": "batch_identifier_789"
}
```

### 4. Get Default Reference Resume Info

**Endpoint:** `GET /Api/default-reference-resume`

**Description:** Returns information about the default reference resume used for comparisons.

**Response:**
```json
{
    "preview": "John Doe - Senior Software Engineer...",
    "length": 2500,
    "file_path": "/path/to/reference/resume.pdf"
}
```

### 5. Get Default Job Description Info

**Endpoint:** `GET /Api/default-jd`

**Description:** Returns information about the default job description used for comparisons.

**Response:**
```json
{
    "preview": "We are seeking a Senior Software Engineer...",
    "length": 1800,
    "file_path": "/path/to/default/jd.pdf"
}
```

## S3 URL Format

The API accepts S3 URLs in the following formats:
- `https://bucket-name.s3.amazonaws.com/path/to/file.pdf`
- `https://bucket-name.s3.region.amazonaws.com/path/to/file.pdf`

The system will automatically convert these to the proper S3 format for processing.

## Error Handling

All endpoints return appropriate HTTP status codes:

- `200`: Success
- `400`: Bad Request (invalid S3 URL, missing parameters)
- `500`: Internal Server Error (processing errors)

Error responses include detailed error messages:
```json
{
    "detail": "Failed to match resume: Invalid S3 URL format"
}
```

## Processing Details

### Text Extraction
- Uses GPT-4 Vision to extract text from PDF files
- Converts PDFs to images for better text recognition
- Handles multi-page documents

### Similarity Calculation
- Uses Azure OpenAI embeddings for semantic similarity
- Calculates cosine similarity between documents
- Provides detailed category-wise breakdown

### Analysis Categories
- **Experience**: Work experience relevance
- **Skills**: Technical and soft skills alignment
- **Education**: Educational background match
- **Certifications**: Professional certifications
- **Projects**: Project experience relevance

## Performance Considerations

- Processing time depends on PDF size and complexity
- Large PDFs may take 30-60 seconds to process
- Multiple resume processing is done sequentially
- Temporary files are automatically cleaned up

## Testing

Use the provided test script to verify endpoint functionality:

```bash
python test_profile_matching.py
```

## Integration Notes

- All endpoints follow the existing logging and error handling patterns
- MongoDB document IDs are used for tracking and debugging
- S3 integration uses the existing AWS credentials and utilities
- The src module is imported dynamically to avoid circular dependencies 