from pydantic import BaseModel, ConfigDict
from typing import List, Dict, Any , Optional



class GenerateQuestionsRequest(BaseModel):
    role: str
    primary_skills: List
    secondary_skills: List
    experience: float
    difficulty_level: str
    no_of_questions: float
    question_type: str  # 'skill' or 'jd'
    job_description: Optional[str] = None

class EvaluateRequest(BaseModel):
    role: str
    primary_skills: List
    secondary_skills: List
    experience: float
    file_content: List
    evaluation_type: str # 'skill', 'jd', or 'hybrid'
    job_description: Optional[str] = None
    
class FakenessEvaluationRequest(BaseModel):
    resume_s3_bucket_path: str = None
    interview_s3_bucket_path: str = None
    high_likelihood_threshold: float 
    medium_likelihood_threshold: float 
    low_likelihood_threshold: float
    mongodb_doc_id:str

class FakenessDeductionRequest(BaseModel):
    comments: str
    likelihood: str
    
class JobDescriptionInput(BaseModel):
    preferred_job_title: str
    employment_status: str
    workplace_type: str
    country: str
    city: str
    experience_in_years: str
    salary_type: str
    primary_skills: list[str]
    secondary_skills: list[str]
    no_of_openings: int
    num_descriptions: Optional[int] = 2

class MultipleJobDescriptionInput(BaseModel):
    jobs: List[JobDescriptionInput]
    num_descriptions: int = 2  

class ResumeExtractionRequest(BaseModel):
    resume_s3_bucket_path: str
    mongodb_doc_id: str
    extract_full_text: bool = False

# New Profile Matching Models
class ProfileMatchingRequest(BaseModel):
    candidate_resume_s3_path: str
    mongodb_doc_id: str

class ProfileMatchingWithJDTextRequest(BaseModel):
    candidate_resume_s3_path: str
    job_description_text: str
    mongodb_doc_id: str

class MultipleProfileMatchingRequest(BaseModel):
    candidate_resume_s3_paths: List[str]
    mongodb_doc_id: str

class MultipleProfileMatchingWithJDTextRequest(BaseModel):
    candidate_resume_s3_paths: List[str]
    job_description_text: str
    mongodb_doc_id: str

class SetReferenceJDTextRequest(BaseModel):
    job_description_text: str

class SetReferenceResumeRequest(BaseModel):
    resume_s3_bucket_path: str

class ProfileMatchingResponse(BaseModel):
    overall_match: float
    category_breakdown: Dict[str, Dict[str, Any]]
    key_matching_highlights: List[str]
    key_differences: List[str]
    missing_or_weak_areas: List[str]
    suggestions_to_improve: List[str]
    mongodb_doc_id: str
    processing_timestamp: str

class Education(BaseModel):
    degree: str
    institution: str
    year: str
    gpa: Optional[str] = None
    major: Optional[str] = None

class Experience(BaseModel):
    company: str
    position: str
    duration: str
    description: Optional[List[str]] = None

class Language(BaseModel):
    language: str
    proficiency: Optional[str] = None

class ResumeExtractionResponse(BaseModel):
    # Personal Information
    first_name: str
    last_name: str
    email: str
    phone_number: str
    location: Optional[str] = None
    linkedin: Optional[str] = None
    website: Optional[str] = None

    # Skills
    primary_skills: List[str]
    secondary_skills: List[str]
    other_skills: List[str]

    # Education and Experience
    education: List[Education]
    experience: List[Experience]
    years_of_experience: float

    # Additional Information
    certifications: Optional[List[str]] = None
    languages: Optional[List[str]] = None
    languages_detailed: Optional[List[Language]] = None
    projects: Optional[List[Dict[str, str]]] = None
    full_text: Optional[str] = None

    # Metadata
    mongodb_doc_id: str
    extraction_timestamp: str
    
    # Extra fields
    model_config = ConfigDict(extra='allow')
