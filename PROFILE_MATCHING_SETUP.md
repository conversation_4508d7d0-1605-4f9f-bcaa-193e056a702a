# Profile Matching Setup Guide

This guide covers setting up the profile matching functionality with separate Azure OpenAI credentials.

## Overview

The profile matching module now supports separate Azure OpenAI credentials using:
- `AZURE_OPENAI_API_KEY_PROFILE` - Profile-specific API key
- `AZURE_OPENAI_ENDPOINT_PROFILE` - Profile-specific endpoint

## Environment Variables

### Profile Matching Specific Variables

```env
# Profile Matching Azure OpenAI Configuration
AZURE_OPENAI_API_KEY_PROFILE=your_profile_specific_api_key
AZURE_OPENAI_ENDPOINT_PROFILE=https://your-profile-resource.openai.azure.com/

# Shared Configuration
AZURE_OPENAI_API_VERSION=2024-12-01-preview
MODEL_NAME=gpt-4o
MODEL_DEPLOYMENT_NAME=gpt-4o
AZURE_DEPLOYMENT_EMBEDDINGS=text-embedding-3-small
AZURE_API_VERSION_EMBEDDINGS=2023-05-15
TEMPERATURE=0.0
TOP_K=5
```

### Fallback Configuration

If profile-specific credentials are not provided, the system will fall back to:
- `AZURE_OPENAI_API_KEY` (default)
- `AZURE_OPENAI_ENDPOINT` (default)

## Local Development Setup

### 1. Environment Configuration

Create a `.env` file in the root directory:

```env
# Profile Matching Specific (Recommended)
AZURE_OPENAI_API_KEY_PROFILE=your_profile_specific_api_key
AZURE_OPENAI_ENDPOINT_PROFILE=https://your-profile-resource.openai.azure.com/

# Fallback (if profile-specific not provided)
AZURE_OPENAI_API_KEY=your_default_api_key
AZURE_OPENAI_ENDPOINT=https://your-default-resource.openai.azure.com/

# Shared Configuration
AZURE_OPENAI_API_VERSION=2024-12-01-preview
MODEL_NAME=gpt-4o
MODEL_DEPLOYMENT_NAME=gpt-4o
AZURE_DEPLOYMENT_EMBEDDINGS=text-embedding-3-small
AZURE_API_VERSION_EMBEDDINGS=2023-05-15
TEMPERATURE=0.0
TOP_K=5

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=your_aws_region

# Application Configuration
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO

# POPPLER_PATH (for local development)
POPPLER_PATH=C:\path\to\your\poppler\bin
```

### 2. Install Dependencies

```bash
# Install main dependencies
pip install -r requirements.txt

# Install the profile_matching module in editable mode
pip install -e app/routers/profile_matching/
```

### 3. Run the Application

```bash
python main.py
```

## Docker Setup

### 1. Build Docker Image

```bash
docker build -t ai-autopilot-service-profile .
```

### 2. Run with Profile-Specific Credentials

```bash
docker run -p 8000:8000 \
  -e AZURE_OPENAI_API_KEY_PROFILE=your_profile_specific_api_key \
  -e AZURE_OPENAI_ENDPOINT_PROFILE=https://your-profile-resource.openai.azure.com/ \
  -e AZURE_OPENAI_API_VERSION=2024-12-01-preview \
  -e MODEL_NAME=gpt-4o \
  -e MODEL_DEPLOYMENT_NAME=gpt-4o \
  -e AZURE_DEPLOYMENT_EMBEDDINGS=text-embedding-3-small \
  -e AZURE_API_VERSION_EMBEDDINGS=2023-05-15 \
  -e TEMPERATURE=0.0 \
  -e TOP_K=5 \
  -e AWS_ACCESS_KEY_ID=your_aws_access_key \
  -e AWS_SECRET_ACCESS_KEY=your_aws_secret_key \
  -e AWS_DEFAULT_REGION=your_aws_region \
  -e POPPLER_PATH="" \
  ai-autopilot-service-profile
```

### 3. Run with Fallback Credentials

```bash
docker run -p 8000:8000 \
  -e AZURE_OPENAI_API_KEY=your_default_api_key \
  -e AZURE_OPENAI_ENDPOINT=https://your-default-resource.openai.azure.com/ \
  -e AZURE_OPENAI_API_VERSION=2024-12-01-preview \
  -e MODEL_NAME=gpt-4o \
  -e MODEL_DEPLOYMENT_NAME=gpt-4o \
  -e AZURE_DEPLOYMENT_EMBEDDINGS=text-embedding-3-small \
  -e AZURE_API_VERSION_EMBEDDINGS=2023-05-15 \
  -e TEMPERATURE=0.0 \
  -e TOP_K=5 \
  -e AWS_ACCESS_KEY_ID=your_aws_access_key \
  -e AWS_SECRET_ACCESS_KEY=your_aws_secret_key \
  -e AWS_DEFAULT_REGION=your_aws_region \
  -e POPPLER_PATH="" \
  ai-autopilot-service-profile
```

## API Endpoints

### Profile Matching Endpoints

1. **Resume-to-Resume Matching**
   ```bash
   POST /Api/match-resume-to-resume
   Content-Type: application/json
   
   {
     "candidate_resume_s3_path": "https://talentwaze.s3.amazonaws.com/candidates-resume/test.pdf",
     "mongodb_doc_id": "test_001"
   }
   ```

2. **Resume-to-JD Matching**
   ```bash
   POST /Api/match-resume-to-jd
   Content-Type: application/json
   
   {
     "candidate_resume_s3_path": "https://talentwaze.s3.amazonaws.com/candidates-resume/test.pdf",
     "mongodb_doc_id": "test_002"
   }
   ```

3. **Multiple Resume Matching**
   ```bash
   POST /Api/multiple-match-resumes-to-jd
   Content-Type: application/json
   
   {
     "candidate_resume_s3_paths": [
       "https://talentwaze.s3.amazonaws.com/candidates-resume/test1.pdf",
       "https://talentwaze.s3.amazonaws.com/candidates-resume/test2.pdf"
     ],
     "mongodb_doc_id": "batch_test_001"
   }
   ```

4. **Default Files Info**
   ```bash
   GET /Api/default-reference-resume
   GET /Api/default-jd
   ```

## Testing

### 1. Check Credential Usage

Look for these log messages when the application starts:
```
Using profile-specific Azure OpenAI credentials (AZURE_OPENAI_API_KEY_PROFILE)
Using profile-specific Azure OpenAI endpoint (AZURE_OPENAI_ENDPOINT_PROFILE)
```

### 2. Test API Endpoints

```bash
# Test default reference resume
curl http://localhost:8000/Api/default-reference-resume

# Test profile matching (replace with real S3 path)
curl -X POST "http://localhost:8000/Api/match-resume-to-resume" \
  -H "Content-Type: application/json" \
  -d '{
    "candidate_resume_s3_path": "https://talentwaze.s3.amazonaws.com/candidates-resume/test.pdf",
    "mongodb_doc_id": "test_001"
  }'
```

## Troubleshooting

### Common Issues

1. **401 Authentication Error**
   - Verify `AZURE_OPENAI_API_KEY_PROFILE` is correct
   - Verify `AZURE_OPENAI_ENDPOINT_PROFILE` is correct
   - Check if the Azure OpenAI resource is active

2. **Missing Protocol Error**
   - Ensure endpoint includes `https://` protocol
   - Example: `https://your-resource.openai.azure.com/`

3. **Fallback to Default Credentials**
   - If profile-specific credentials are not set, check logs for fallback message
   - Ensure default credentials are properly configured

### Log Messages

- ✅ **Success**: "Using profile-specific Azure OpenAI credentials"
- ⚠️ **Fallback**: "Using default Azure OpenAI credentials"
- ❌ **Error**: "Error code: 401 - Access denied due to invalid subscription key"

## Configuration Priority

The system uses the following priority for Azure OpenAI credentials:

1. **Profile-specific** (highest priority):
   - `AZURE_OPENAI_API_KEY_PROFILE`
   - `AZURE_OPENAI_ENDPOINT_PROFILE`

2. **Default** (fallback):
   - `AZURE_OPENAI_API_KEY`
   - `AZURE_OPENAI_ENDPOINT`

This allows you to use separate Azure OpenAI resources for profile matching while maintaining compatibility with existing configurations. 