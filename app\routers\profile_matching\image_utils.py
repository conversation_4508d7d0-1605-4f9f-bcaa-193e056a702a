from pdf2image import convert_from_path
import base64
import io
import os
from .constants import POPPLER_PATH

# Add poppler to PATH if specified and path exists
if POPPLER_PATH and POPPLER_PATH.strip():
    # Check if the path exists (for local development)
    if os.path.exists(POPPLER_PATH):
        os.environ["PATH"] += os.pathsep + POPPLER_PATH
    # For Docker, poppler-utils is already in PATH via apt-get install

def convert_pdf_to_images(pdf_path):
    """Converts each page of a PDF to an image using pdf2image."""
    print(f"Converting PDF to images: {pdf_path}")
    images = []
    try:
        # Convert PDF to images using pdf2image
        images = convert_from_path(pdf_path)
        print(f"Successfully converted {len(images)} pages to images")
        return images
    except Exception as e:
        print(f"Error converting PDF to images: {e}")
        return []

def convert_image_to_base64(image):
    """Converts a PIL Image to a Base64 string."""
    try:
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
        return img_str
    except Exception as e:
        print(f"Error converting image to base64: {e}")
        return None

