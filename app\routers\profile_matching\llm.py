from .log_manager import logger
from .project_config import get_project_config
from .prompt import get_prompt
from dotenv import load_dotenv
from functools import lru_cache
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI
import traceback

config = get_project_config()

vision_chat = None
chat = None
llm = None
# Load environment variables
load_dotenv()

@lru_cache
def get_llm() -> AzureChatOpenAI:
    """
    Initializes and returns an instance of AzureChatOpenAI with the specified configuration.

    Raises:
        RuntimeError: If there is an error during the initialization of AzureChatOpenAI.

    Returns:
        AzureChatOpenAI: An instance of the AzureChatOpenAI model configured with the specified parameters.
    """
    logger.debug("Initializing AzureChatOpenAI instance")
    try:
        llm = AzureChatOpenAI(
            temperature=config.TEMPERATURE, 
            model_name=config.MODEL_NAME,   # type: ignore
            deployment_name=config.MODEL_DEPLOYMENT_NAME, # type: ignore
            azure_endpoint=config.AZURE_OPENAI_ENDPOINT,
            api_key=config.AZURE_OPENAI_API_KEY,
            api_version=config.AZURE_OPENAI_API_VERSION,
        )
        logger.debug("AzureChatOpenAI instance created successfully")
    except Exception as exe:
        logger.error(f"Error initializing AzureChatOpenAI: {exe}", exc_info=True)
        raise RuntimeError(f"Initialization failed for AzureChatOpenAI due to: {exe}")
    return llm

@lru_cache
def get_chat_model():
    return get_llm()

@lru_cache
def get_vision_model():
    return get_llm()

def extract_text_from_image(image_base64):
    """
    Extract text from an image using the vision model.
    
    Args:
        image_base64 (str): Base64-encoded image
        
    Returns:
        str: Extracted text from the image
    """
    global vision_chat
    if vision_chat is None:
        vision_chat = get_vision_model()
    try:
        logger.info("Extracting text from image using vision model")
        
        # Create the message with the image
        image_url = f"data:image/png;base64,{image_base64}"
        
        messages = [
            SystemMessage(content="You are a document text extraction assistant. Extract all text from the image exactly as it appears, preserving formatting as much as possible."),
            HumanMessage(content=[
                {"type": "text", "text": "Extract all the text from this image of a document. Preserve the formatting as much as possible."},
                {"type": "image_url", "image_url": {"url": image_url}}
            ])
        ]
        
        logger.info("Sending image to vision model")
        response = vision_chat.invoke(messages)
        
        content = response.content
        logger.info("Text extracted successfully from image")
        
        return content
    except Exception as e:
        logger.error(f"Error extracting text from image: {str(e)}")
        logger.error(traceback.format_exc())
        return f"Error: Failed to extract text from image: {str(e)}"



def get_llm_response_with_similarity(reference_text, candidate_text, similarity_score):
    """
    Get LLM response for resume comparison with embedding similarity score.
    
    Args:
        reference_text (str): The reference resume or job description text
        candidate_text (str): The candidate resume text
        similarity_score (float): The embedding similarity score (0-1)
        
    Returns:
        str: The LLM's analysis response
    """
    global chat
    if chat is None:
        chat = get_chat_model()
    try:
        logger.info("Preparing LLM request for resume comparison with similarity score")
        
        prompt = get_prompt(reference_text, candidate_text, similarity_score)
        
        messages = [
            SystemMessage(content="You are a resume analysis expert. And you provide higher weight to a sales profile with a higher similarity score and provide below 50% match for a non-sales profile."),
            HumanMessage(content=prompt)
        ]
        
        logger.info("Sending request to Azure OpenAI")
        response = chat.invoke(messages)
        
        content = response.content
        logger.info("LLM response received successfully")
        
        return str(content)
    except Exception as e:
        logger.error(f"Error getting LLM response: {str(e)}")
        logger.error(traceback.format_exc())
        return f"Error: Failed to get response from Azure OpenAI: {str(e)}"

# Keep the LLMHandler class for backward compatibility
class LLMHandler:
    """Handles all LLM interactions for the application."""
    
    def __init__(self):
        """Initialize the LLM handler with Azure OpenAI."""
        try:
            logger.info("Initializing LLMHandler...")
            self.chat = get_chat_model()
            self.vision_chat = get_vision_model()
            logger.info("LLMHandler initialized successfully")
        except Exception as e:
            logger.error(f"Initialization failed for LLMHandler: {str(e)}")
            logger.error(traceback.format_exc())
            raise RuntimeError(f"Initialization failed for LLMHandler due to: {e}")
    
    def extract_text_from_image(self, image_base64):
        """Extract text from an image using the vision model."""
        return extract_text_from_image(image_base64)
    
    def get_chat_instance(self):
        """Return the chat instance for direct use."""
        return self.chat
    
    def get_llm_response_with_similarity(self, reference_text, candidate_text, similarity_score):
        """Get LLM response for resume comparison with similarity score."""
        return get_llm_response_with_similarity(reference_text, candidate_text, similarity_score)




