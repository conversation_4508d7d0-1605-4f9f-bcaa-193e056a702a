import boto3
import os
from app.routers.fakeness_detection_module.Modular_faceMotion import FaceMotionDetection
from app.routers.fakeness_detection_module.optimized_with_multi_processing import Dlib_FaceRecognition

from app.routers.fakeness_detection_module.no_diarization_speakerR<PERSON>og import SpeakerRecognition
from app.routers.fakeness_detection_module.final_decision import Final_FakenessScore
from dotenv import load_dotenv
load_dotenv()
import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
logger = logging.getLogger(__name__)


os.environ["AWS_ACCESS_KEY_ID"] = os.getenv("AWS_ACCESS_KEY_ID")
os.environ["AWS_SECRET_ACCESS_KEY"] = os.getenv("AWS_SECRET_ACCESS_KEY")
os.environ['AWS_DEFAULT_REGION'] = os.getenv('AWS_DEFAULT_REGION')

# AWS S3 Client
s3 = boto3.client('s3')

def parse_s3_path(s3_path):
    """
    Parse the S3 path into bucket name and key.
    Example: s3://bucket-name/folder/file -> bucket-name, folder/file
    """
    s3_components = s3_path.replace("s3://", "").split("/", 1)
    # print(f"bucketname and folder path : {s3_components[0]}, {s3_components[1]}")
    return s3_components[0], s3_components[1]


def download_from_s3(bucket_name, s3_key):
    """
    Download a video file from S3 and save it locally.
    """
    video_local_path = os.path.join("/tmp", bucket_name+"_"+s3_key.split('-')[0].replace('/', "_")+".webm")
    logger.info(f"the downloaded video got saved at : {video_local_path}")
    s3.download_file(bucket_name, s3_key, video_local_path)
    return video_local_path


def process_with_face_motion(video_path):

    # Your faceMotion.py logic comes here (adjust as needed for the correct paths and variables)
    video_path = video_path.split('.')[0]+".mp4" # changing the extension from webm to mp4
    # print("The facemotion detection module object is getting initialized with the following video_path", video_path)
    fmd = FaceMotionDetection(video_path)
    # print("The process_video method is called")
    output_json_path, output_video_path = fmd.process_video()

    return output_json_path, output_video_path


def upload_to_s3(file_path, bucket_name, s3_key):
    """
    Upload a file to S3 and return the file URL.
    """
    s3.upload_file(file_path, bucket_name, s3_key)
    return f"s3://{bucket_name}/{s3_key}"