from dotenv import load_dotenv
import logging
import os

load_dotenv()

PROJECT_NAME = "src"
DEFAULT_LEVEL = logging.INFO
LOGGING_STR = "[%(asctime)s: %(levelname)s: %(module)s: %(lineno)d]: %(message)s"

# Set dynamic logging level via environment variable (if needed)
level_name = os.getenv("LOG_LEVEL", "INFO")
LEVEL = getattr(logging, level_name.upper(), DEFAULT_LEVEL)

# Create a custom logger
logger = logging.getLogger(PROJECT_NAME)
logger.setLevel(LEVEL)

# Define console handler with detailed format
console_handler = logging.StreamHandler()
console_handler.setLevel(LEVEL)
formatter = logging.Formatter(LOGGING_STR)
console_handler.setFormatter(formatter)

# Add the handler to the logger if not already present
if not logger.handlers:
    logger.addHandler(console_handler)

# Optionally, add file logging here, e.g. using FileHandler
# file_handler = logging.FileHandler('app.log')
# file_handler.setLevel(LEVEL)
# file_handler.setFormatter(formatter)
# logger.addHandler(file_handler)